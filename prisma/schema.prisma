generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Company {
  id               String   @id @default(auto()) @map("_id") @db.ObjectId
  name             String   @unique
  agencies         Agency[]
  logo             String?
  isSetupCompleted Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

model Agency {
  id           String         @id @default(auto()) @map("_id") @db.ObjectId
  name         String
  location     String
  isHeadOffice Boolean        @default(false)
  email        String?        @unique
  phone        String?        @unique
  schedules    Schedule[]
  agents       Agent[]        @relation(name: "AgencyAgents")
  manager      AgencyManager?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  company      Company?       @relation(fields: [companyId], references: [id])
  companyId    String?        @db.ObjectId
  applicants   Applicant[]
  // users        User[]         // All users can be associated with an agency
}

model Country {
  id         String      @id @default(auto()) @map("_id") @db.ObjectId
  name       String      @unique
  code       String
  capital    String?
  population Int?
  flag       String
  currency   String
  applicants Applicant[]
  timezone   String
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  forms      Form[]
}

// Base user model for authentication and common fields
model User {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  name         String
  email        String?  @unique
  phone        String?  @unique
  password     String
  role         Role     @default(AGENT)
  referralCode String?
  activated    Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relationships
  profile         Profile?
  notifications   Notification[]
  board           Board?
  tasks           Task[]
  formSubmissions FormSubmission[]
  applicants      Applicant[] // All users can create applicants
  // agency          Agency?          @relation(fields: [agencyId], references: [id])
  // agencyId        String?          @db.ObjectId

  // Role-specific relationships
  agent          Agent?
  agencyManager  AgencyManager?
  generalManager GeneralManager?
}

model Profile {
  id          String     @id @default(auto()) @map("_id") @db.ObjectId
  firstName   String?
  lastName    String?
  documents   Document[]
  avatar      String?
  createdAt   DateTime   @default(now())
  verified    Boolean    @default(false)
  dateOfBirth DateTime?
  updatedAt   DateTime   @updatedAt

  // One-to-one with User
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String @unique @db.ObjectId
}

// Agent-specific model
model Agent {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String   @unique @db.ObjectId
  agency    Agency?  @relation(name: "AgencyAgents", fields: [agencyId], references: [id])
  agencyId  String?  @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Agency Manager model
model AgencyManager {
  id          String @id @default(auto()) @map("_id") @db.ObjectId
  user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String @unique @db.ObjectId
  permissions Json? // Manager-specific permissions

  agency   Agency? @relation(fields: [agencyId], references: [id])
  agencyId String? @unique @db.ObjectId

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// General Manager model  
model GeneralManager {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String   @unique @db.ObjectId
  permissions Json? // GM-specific permissions
  accessLevel String   @default("FULL") // FULL, LIMITED, etc.
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Document {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  name      String
  frontSide String?
  backSide  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  profile   Profile? @relation(fields: [profileId], references: [id])
  profileId String?  @db.ObjectId
}

model Schedule {
  id             String     @id @default(auto()) @map("_id") @db.ObjectId
  title          String
  description    String?
  date           DateTime
  isRead         Boolean    @default(false)
  approvedClient Boolean    @default(false)
  approvedAgent  Boolean    @default(false)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  applicant      Applicant? @relation(fields: [applicantId], references: [id])
  applicantId    String?    @db.ObjectId
  agency         Agency?    @relation(fields: [agencyId], references: [id])
  agencyId       String?    @db.ObjectId

  @@map("schedules")
}

model Notification {
  id          String     @id @default(auto()) @map("_id") @db.ObjectId
  message     String
  type        String
  read        Boolean    @default(false)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  user        User?      @relation(fields: [userId], references: [id])
  userId      String?    @db.ObjectId
  applicant   Applicant? @relation(fields: [applicantId], references: [id])
  applicantId String?    @db.ObjectId

  @@map("notifications")
}

model Task {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  status      String
  priority    String
  dueDate     DateTime?
  column      Column    @relation(fields: [columnId], references: [id])
  columnId    String    @db.ObjectId
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  user        User?     @relation(fields: [userId], references: [id])
  userId      String?   @db.ObjectId

  @@map("tasks")
}

model Column {
  id      String @id @default(auto()) @map("_id") @db.ObjectId
  title   String
  order   Int
  tasks   Task[]
  board   Board  @relation(fields: [boardId], references: [id])
  boardId String @db.ObjectId

  @@map("columns")
}

model Board {
  id      String   @id @default(auto()) @map("_id") @db.ObjectId
  title   String
  columns Column[]
  user    User     @relation(fields: [userId], references: [id])
  userId  String   @unique @db.ObjectId

  @@unique([userId, id])
  @@map("boards")
}

model Form {
  id          String           @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  country     Country?         @relation(fields: [countryId], references: [id])
  countryId   String?          @db.ObjectId
  fields      FormField[]
  submissions FormSubmission[]
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  isPublished Boolean          @default(false)

  @@map("forms")
}

model FormField {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  label        String
  type         String
  key          String?  @unique
  required     Boolean  @default(false)
  options      Json?
  placeholder  String?
  helpText     String?
  validation   Json?
  defaultValue Json?
  order        Int
  form         Form     @relation(fields: [formId], references: [id], onDelete: Cascade)
  formId       String   @db.ObjectId
  isArray      Boolean  @default(false)
  arrayConfig  Json?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("form_fields")
}

model Source {
  id         String      @id @default(auto()) @map("_id") @db.ObjectId
  name       String      @unique
  applicants Applicant[]
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  @@map("sources")
}

model FormSubmission {
  id              String     @id @default(auto()) @map("_id") @db.ObjectId
  formId          String     @db.ObjectId
  data            Json
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt
  form            Form?      @relation(fields: [formId], references: [id])
  submittedBy     String?    @db.ObjectId
  submittedByUser User?      @relation(fields: [submittedBy], references: [id])
  applicant       Applicant? @relation(fields: [applicantId], references: [id])
  applicantId     String?    @db.ObjectId

  @@map("form_submissions")
}

model States {
  id         String      @id @default(auto()) @map("_id") @db.ObjectId
  title      String
  state      String      @unique
  order      Int
  applicants Applicant[]
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  @@map("states")
}

model Applicant {
  id             String  @id @default(auto()) @map("_id") @db.ObjectId
  code           String? @unique
  clientEmail    String? @unique
  clientPhone    String // Fixed typo: was "cleintPhone"
  clientFullName String

  // Relationships
  states        States?          @relation(fields: [statesId], references: [id])
  statesId      String?          @db.ObjectId
  user          User?            @relation(fields: [userId], references: [id]) // Created by user
  userId        String?          @db.ObjectId
  userInfo      FormSubmission[]
  notifications Notification[]
  schedules     Schedule[]
  agency        Agency?          @relation(fields: [agencyId], references: [id])
  agencyId      String?          @db.ObjectId
  source        Source?          @relation(fields: [sourceId], references: [id])
  sourceId      String?          @db.ObjectId
  country       Country?         @relation(fields: [countryId], references: [id])
  countryId     String?          @db.ObjectId

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("applicants")
}

enum Role {
  AGENCY_MANAGER
  AGENT
  GENERAL_MANAGER
}
