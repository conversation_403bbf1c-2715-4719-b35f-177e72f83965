import { z } from "zod";

export const mangerSchema = z.object({
  name: z.string().min(1, "Name is required"),
  phone: z.string().min(1, "Phone is required"),
  password: z.string().min(1, "Password is required"),
  email: z
    .email("Please enter a valid email address")
    .min(1, "Email is required"),
  agencyId: z.string().min(1, "Agency is required"),
});

export type Manger = z.infer<typeof mangerSchema>;
