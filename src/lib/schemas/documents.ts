import { z } from "zod";

export const agentDocuments = z.object({
  idCardFront: z
    .instanceof(File)
    .refine((file) => file.size <= 5000000, `Max file size is 5MB`)
    .refine(
      (file) =>
        ["image/jpeg", "image/png", "image/jpg", "application/pdf"].includes(
          file.type
        ),
      "Only .jpg, .jpeg, .png and .pdf formats are supported"
    ),
  idCardBack: z
    .instanceof(File)
    .refine((file) => file.size <= 5000000, `Max file size is 5MB`)
    .refine(
      (file) =>
        ["image/jpeg", "image/png", "image/jpg", "application/pdf"].includes(
          file.type
        ),
      "Only .jpg, .jpeg, .png and .pdf formats are supported"
    ),
  locationPlan: z
    .instanceof(File)
    .refine((file) => file.size <= 5000000, `Max file size is 5MB`)
    .refine(
      (file) =>
        ["image/jpeg", "image/png", "image/jpg", "application/pdf"].includes(
          file.type
        ),
      "Only .jpg, .jpeg, .png and .pdf formats are supported"
    ),
});

export type AgentsDocument = z.infer<typeof agentDocuments>;
