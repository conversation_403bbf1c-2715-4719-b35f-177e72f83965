import { z } from "zod";

export const agentSchema = z
  .object({
    email: z.email("Invalid email"),
    username: z.string().min(1, "Username is required"),
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z.string().min(1, "Confirm password is required"),
    fullName: z.string().min(1, "Full name is required"),
    idNumber: z.string().min(1, "ID number is required"),
    phoneNumber: z.string().min(1, "Phone number is required"),
    dateOfBirth: z.string().min(1, "Date of birth is required"),
    address: z.string().min(1, "Address is required"),
    idCardFile: z.any().optional(),
    locationPlacardFile: z.any().optional(),
    agencyId: z.string().min(1, "Agency is required"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });
