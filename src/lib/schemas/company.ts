import { z } from "zod";

export const companySchema = z
  .object({
    logo: z.any(),
    name: z.string().min(1, "Name is required"),
    phone: z.string().min(1, "Phone is required"),
    email: z.email("Please enter a valid email address"),
    address: z.string().min(1, "Address is required"),
    agencyName: z.string().min(1, "Name is required"),
    managerName: z.string().min(1, "Name is required"),
    managerPhone: z.string().min(1, "Phone is required"),
    managerEmail: z.email("Please enter a valid email address"),
    managerPassword: z.string().min(1, "Password is required"),
    managerConfirmPassword: z.string().min(1, "Confirm Password is required"),
  })
  .refine((data) => data.managerPassword === data.managerConfirmPassword, {
    message: "Passwords do not match",
    path: ["managerConfirmPassword"],
  });

export type Company = z.infer<typeof companySchema>;
