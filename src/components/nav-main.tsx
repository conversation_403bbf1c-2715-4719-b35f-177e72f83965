"use client";

import { type LucideIcon } from "lucide-react";
import { usePathname } from "next/navigation";

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import Link from "next/link";
import { cn } from "@/lib/utils";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon: LucideIcon;
  }[];
}) {
  const pathname = usePathname();

  // Find the most specific matching item to prevent multiple active states
  const getActiveItem = () => {
    // First, check for exact matches
    const exactMatch = items.find((item) => pathname === item.url);
    if (exactMatch) return exactMatch.url;

    // Then, find the longest matching path (most specific)
    const pathMatches = items
      .filter((item) => pathname.startsWith(item.url + "/"))
      .sort((a, b) => b.url.length - a.url.length);

    return pathMatches.length > 0 ? pathMatches[0].url : null;
  };

  const activeItemUrl = getActiveItem();

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isActive = item.url === activeItemUrl;

          return (
            <SidebarMenuItem key={item.url}>
              <SidebarMenuButton
                asChild
                isActive={isActive}
                className={cn(
                  "transition-colors duration-200",
                  isActive &&
                    "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                )}
              >
                <Link href={item.url}>
                  <item.icon
                    className={cn(
                      "transition-colors",
                      isActive && "text-sidebar-primary"
                    )}
                  />
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
