import { Agency } from "@/types/agency";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "./ui/card";
import {
  Calendar,
  Crown,
  Edit,
  Eye,
  Mail,
  MapPin,
  MoreHorizontal,
  Phone,
  Users,
} from "lucide-react";
import { Badge } from "./ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Button } from "./ui/button";

interface AgencyCardProps {
  agency: Agency;
  onEdit?: (agency: Agency) => void;
  onView?: (agency: Agency) => void;
}

export const AgencyCard: React.FC<AgencyCardProps> = ({
  agency,
  onEdit,
  onView,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card className="rounded-none shadow-none duration-200 relative group">
      {/* Head Office Crown */}
      {agency.isHeadOffice && (
        <div className="absolute -top-2 -right-2 z-10">
          <div className="bg-yellow-500 rounded-full p-2 shadow-md">
            <Crown className="h-4 w-4 text-white" />
          </div>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {/* Agency Avatar */}
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm">
              {getInitials(agency.name)}
            </div>

            <div className="flex-1">
              <CardTitle className="text-lg font-semibold text-gray-900">
                {agency.name}
              </CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge
                  variant={agency.isHeadOffice ? "default" : "secondary"}
                  className="text-xs"
                >
                  {agency.isHeadOffice ? "Head Office" : "Branch"}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {agency.agents.length} Agent
                  {agency.agents.length !== 1 ? "s" : ""}
                </Badge>
              </div>
            </div>
          </div>

          {/* Actions Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48 rounded-none">
              <DropdownMenuItem onClick={() => onView?.(agency)}>
                <Eye className="w-4 h-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit?.(agency)}>
                <Edit className="w-4 h-4 mr-2" />
                Edit Agency
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Users className="w-4 h-4 mr-2" />
                Manage Agents
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-4">
        {/* Contact Information */}
        <div className="space-y-3">
          <div className="flex items-center gap-3 text-sm text-gray-600">
            <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <span className="truncate">{agency.location}</span>
          </div>

          <div className="flex items-center gap-3 text-sm text-gray-600">
            <Mail className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <a
              href={`mailto:${agency.email}`}
              className="text-blue-600 hover:underline truncate"
            >
              {agency.email}
            </a>
          </div>

          <div className="flex items-center gap-3 text-sm text-gray-600">
            <Phone className="h-4 w-4 text-gray-400 flex-shrink-0" />
            <a
              href={`tel:${agency.phone}`}
              className="text-blue-600 hover:underline"
            >
              {agency.phone}
            </a>
          </div>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Users className="h-4 w-4" />
            <span>{agency.agents.length} Active Agents</span>
          </div>

          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Calendar className="h-4 w-4" />
            <span>Since {formatDate(agency.createdAt)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
