import React from "react";
interface formHeaderProps {
  title: string;
  subtitle?: string;
  description: string;
}
export function FormHeader({ title, description, subtitle }: formHeaderProps) {
  return (
    <div>
      <h1 className="text-2xl font-bold">{title}</h1>
      {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
}
