"use client";

import * as React from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useSession } from "next-auth/react";
// This is sample data.
const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/johndoe.jpg",
  },
  teams: [],
  navMain: [
    {
      title: "Dashboard",
      url: "/main",
      icon: Pie<PERSON>hart,
    },

    {
      title: "Applications",
      url: "/main/applications",
      icon: GalleryVerticalEnd,
    },
    {
      title: "Appointments",
      url: "/main/appointments",
      icon: BookO<PERSON>,
    },
    {
      title: "Forms",
      url: "/main/forms",
      icon: Command,
    },
    {
      title: "Tasks",
      url: "/main/tasks",
      icon: AudioWaveform,
    },
    {
      title: "Managers",
      url: "/main/managers",
      icon: Frame,
    },
    {
      title: "Agents",
      url: "/main/agents",
      icon: Bot,
    },
    {
      title: "Countries",
      url: "/main/countries",
      icon: SquareTerminal,
    },
    {
      title: "Agencies",
      url: "/main/agencies",
      icon: Map,
    },
  ],
  projects: [
    {
      name: "Logout",
      url: "/auth/logout",
      icon: Map,
    },
    {
      name: "Settings",
      url: "/settings",
      icon: Settings2,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();
  if (!session) return <></>;
  data.user.name = session.user.name || "John Doe";
  data.user.email = session.user.email || "<EMAIL>";
  data.user.avatar = session.user.image || "/avatars/johndoe.jpg";

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
