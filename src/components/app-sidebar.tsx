"use client";

import * as React from "react";
import {
  AudioWaveform,
  BookOpen,
  Bot,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  Settings2,
  SquareTerminal,
  Plus,
  Search,
  Bell,
  HelpCircle,
  Zap,
} from "lucide-react";

import { NavMain } from "@/components/nav-main";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
// This is sample data.
const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/johndoe.jpg",
  },
  teams: [],
  navMain: [
    {
      title: "Dashboard",
      url: "/main",
      icon: <PERSON><PERSON><PERSON>,
    },

    {
      title: "Applications",
      url: "/main/applications",
      icon: GalleryVerticalEnd,
    },
    {
      title: "Appointments",
      url: "/main/appointments",
      icon: BookOpen,
    },
    {
      title: "Forms",
      url: "/main/forms",
      icon: Command,
    },
    {
      title: "Tasks",
      url: "/main/tasks",
      icon: AudioWaveform,
    },
    {
      title: "Managers",
      url: "/main/managers",
      icon: Frame,
    },
    {
      title: "Agents",
      url: "/main/agents",
      icon: Bot,
    },
    {
      title: "Countries",
      url: "/main/countries",
      icon: SquareTerminal,
    },
    {
      title: "Agencies",
      url: "/main/agencies",
      icon: Map,
    },
  ],
  projects: [
    {
      name: "Settings",
      url: "/main/settings",
      icon: Settings2,
    },
  ],
  quickActions: [
    {
      name: "Quick Search",
      icon: Search,
      action: "search",
    },
    {
      name: "Create New",
      icon: Plus,
      action: "create",
    },
    {
      name: "Notifications",
      icon: Bell,
      action: "notifications",
    },
    {
      name: "Help & Support",
      icon: HelpCircle,
      action: "help",
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { data: session } = useSession();

  const handleQuickAction = (action: string) => {
    switch (action) {
      case "search":
        toast.info("Quick search functionality coming soon!");
        break;
      case "create":
        toast.info("Create new item functionality coming soon!");
        break;
      case "notifications":
        toast.info("Notifications panel coming soon!");
        break;
      case "help":
        toast.info("Help & support center coming soon!");
        break;
      default:
        toast.info("Action not implemented yet!");
    }
  };

  if (!session) return <></>;
  data.user.name = session.user.name || "John Doe";
  data.user.email = session.user.email || "<EMAIL>";
  data.user.avatar = session.user.image || "/avatars/johndoe.jpg";

  return (
    <Sidebar
      collapsible="icon"
      className="sidebar-gradient border-r-2 border-sidebar-border"
      {...props}
    >
      <SidebarHeader className="border-b border-sidebar-border/50">
        <TeamSwitcher teams={data.teams} />

        {/* Quick Actions */}
        <SidebarGroup className="mt-4 pb-4">
          <SidebarGroupContent>
            <SidebarMenu>
              <div className="grid grid-cols-2 gap-2 p-2">
                {data.quickActions.map((action) => (
                  <SidebarMenuItem key={action.action}>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-10 w-full justify-start gap-2 text-sidebar-foreground hover:bg-sidebar-primary/10 hover:text-sidebar-primary border border-sidebar-border/30 hover:border-sidebar-primary/30 transition-all duration-200"
                      onClick={() => handleQuickAction(action.action)}
                    >
                      <action.icon className="h-4 w-4" />
                      <span className="text-xs group-data-[collapsible=icon]:hidden font-medium">
                        {action.name.split(" ")[0]}
                      </span>
                    </Button>
                  </SidebarMenuItem>
                ))}
              </div>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarHeader>

      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
      </SidebarContent>

      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
