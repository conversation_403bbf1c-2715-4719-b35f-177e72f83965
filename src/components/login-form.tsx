"use client";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Login, loginSchema } from "@/lib/schemas/login";
import { useState, useTransition } from "react";
import { toast } from "sonner";
import { login } from "@/actions/login";
import { Eye, EyeOff } from "lucide-react";
import { useSearchParams } from "next/navigation";

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div">) {
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | undefined>();
  const [showPassword, setShowPassword] = useState(false);

  const searchParam = useSearchParams();
  const returnUrl = searchParam.get("returnUrl");

  console.log(returnUrl);

  const { register, handleSubmit, formState } = useForm({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleLogin = (data: Login) => {
    setError(undefined);
    startTransition(() => {
      login(data, returnUrl!).then((res) => {
        if (res?.error) {
          toast.error(res.error);
          setError(res.error);
        }
      });
    });
  };
  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-2">
          <h1 className="text-xl font-bold">Welcome Back</h1>
          <p className="text-muted-foreground">Login to your account</p>
          <div className="text-center text-sm">
            Forgot Password {""}
            <a href="#" className="underline underline-offset-4">
              Reset Password
            </a>
          </div>
        </div>
        <form onSubmit={handleSubmit(handleLogin)}>
          <div className="flex flex-col gap-6">
            <div className="grid gap-3">
              <Label htmlFor="email">Email</Label>
              <Input
                {...register("email")}
                id="email"
                type="email"
                placeholder="<EMAIL>"
              />
              {formState.errors.email && (
                <span className="text-destructive text-sm">
                  {formState.errors.email.message}
                </span>
              )}
            </div>

            <div className="grid gap-3">
              <Label htmlFor="email">Password</Label>
              <div className="flex flex-col gap-2 relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="********"
                  {...register("password")}
                />
                <div className="flex justify-end absolute right-3 top-0 h-full ">
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <Eye size={16} /> : <EyeOff size={16} />}
                  </button>
                </div>
              </div>
              {formState.errors.password && (
                <span className="text-destructive text-sm">
                  {formState.errors.password.message}
                </span>
              )}
            </div>
            <Button type="submit" className="w-full" disabled={isPending}>
              {isPending ? "Loging in ......" : " Login"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
