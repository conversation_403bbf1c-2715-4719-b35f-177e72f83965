import Image from "next/image";
import React from "react";
import { But<PERSON> } from "./ui/button";
interface props {
  text?: string;
  onClick?: () => void;
  buttonText?: string;
  isLoading?: boolean;
}
export default function EmptyList({
  text,
  onClick,
  buttonText,
  isLoading,
}: props) {
  return (
    <div className="text-center min-h-[80vh] justify-center items-center flex">
      <div className="flex flex-col justify-center items-center gap-1">
        <Image
          src="/images/empty-list.png"
          alt="Empty List"
          width={90}
          height={90}
        />
        <h1 className="text-2xl font-bold">OOPS</h1>
        <p className="text-muted-foreground">{text || "No data found"}</p>
        {onClick && (
          <Button variant={"secondary"} onClick={onClick} disabled={isLoading}>
            {buttonText || "Add Data"}
          </Button>
        )}
      </div>
    </div>
  );
}
