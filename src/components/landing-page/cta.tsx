"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { LoadingSpinner } from "../loading-spinner";

export default function CTA() {
  const { data: session, status } = useSession();
  const user = session?.user;
  const isLoading = status === "loading";
  return (
    <div className="mt-40">
      <div className="w-[60%] flex flex-col justify-center items-center mx-auto">
        <Badge className="rounded-none">Eureka</Badge>
        <h1 className="text-6xl leading-normal text-center font-semibold">
          Eureka Immigration Agency Client Management
        </h1>

        <p className="text-lg text-gray-600 text-center mt-4 mb-8">
          Streamline your immigration journey with our comprehensive client
          management system. We help connect clients with experienced
          immigration agents for a smoother visa application process.
        </p>

        {isLoading ? (
          <div className="">
            <LoadingSpinner text="Loading..." />
          </div>
        ) : (
          <div className="flex gap-x-4">
            {session?.user ? (
              <div className="flex items-center gap-x-2">
                {user?.role === "AGENT" ||
                user?.role === "MANAGER" ||
                user?.role === "GENERAL_MANAGER" ? (
                  <Button asChild size={"lg"}>
                    <Link href="/main">Dashboard</Link>
                  </Button>
                ) : (
                  <Button asChild size={"lg"}>
                    <Link href="/client">Client Portal</Link>
                  </Button>
                )}
              </div>
            ) : (
              <>
                <Button variant={"outline"} size={"lg"}>
                  <Link href="/auth/client">Client</Link>
                </Button>
                <Button size={"lg"}>
                  <Link href="/auth/login">Agent</Link>
                </Button>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
