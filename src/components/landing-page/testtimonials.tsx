import React from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { MessageCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
export default function Testimonials() {
  return (
    <div className="mt-32">
      <h2 className="text-4xl font-semibold text-center mb-16">Testimonials</h2>
      <div className="max-w-4xl mx-auto px-4">
        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full"
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
              <Card className="text-center rounded-none p-6 h-full">
                <CardHeader>
                  <MessageCircle />
                  <CardTitle className="text-xl font-medium">
                    <PERSON>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Eureka made the visa application process much easier. The
                    agent was knowledgeable and provided excellent support
                  </p>
                </CardContent>
              </Card>
            </CarouselItem>
            <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
              <Card className="text-center rounded-none p-6 h-full">
                <CardHeader>
                  <MessageCircle />
                  <CardTitle className="text-xl font-medium">
                    Jane Smith
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    I was impressed with the agents professionalism and the
                    transparency throughout the process.
                  </p>
                </CardContent>
              </Card>
            </CarouselItem>
            <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
              <Card className="text-center rounded-none p-6 h-full">
                <CardHeader>
                  <MessageCircle />
                  <CardTitle className="text-xl font-medium">
                    Bob Johnson
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    The agent expertise and guidance were invaluable. I could
                    not have done it without them.
                  </p>
                </CardContent>
              </Card>
            </CarouselItem>
            <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
              <Card className="text-center rounded-none p-6 h-full">
                <CardHeader>
                  <MessageCircle />
                  <CardTitle className="text-xl font-medium">
                    Sarah Wilson
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    The platform made it easy to find the right agent for my
                    specific needs. Highly recommend their services!
                  </p>
                </CardContent>
              </Card>
            </CarouselItem>
            <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
              <Card className="text-center rounded-none p-6 h-full">
                <CardHeader>
                  <MessageCircle />
                  <CardTitle className="text-xl font-medium">
                    Michael Chen
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Excellent communication and support throughout my visa
                    application. The process was smooth and stress-free.
                  </p>
                </CardContent>
              </Card>
            </CarouselItem>
            <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
              <Card className="text-center rounded-none p-6 h-full">
                <CardHeader>
                  <MessageCircle />
                  <CardTitle className="text-xl font-medium">
                    Emily Rodriguez
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">
                    Professional service with clear guidance at every step. Made
                    my immigration journey much more manageable.
                  </p>
                </CardContent>
              </Card>
            </CarouselItem>
          </CarouselContent>
          <CarouselPrevious className="hidden md:flex" />
          <CarouselNext className="hidden md:flex" />
        </Carousel>
      </div>
    </div>
  );
}
