import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";

export default function Features() {
  return (
    <div className="mt-32">
      <h2 className="text-4xl font-semibold text-center mb-4">Key Features</h2>
      <p className="text-lg text-gray-600 text-center mb-16 max-w-2xl mx-auto">
        Discover the powerful tools and services that make your immigration
        journey smoother and more efficient
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <Card className="text-center rounded-none p-8 hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
          <CardHeader className="pb-4">
            <div className="mb-4 text-4xl">📝</div>
            <CardTitle className="text-xl font-semibold mb-2">
              Easy Documentation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 leading-relaxed">
              Simplified document submission and tracking for visa applications
              with secure cloud storage
            </p>
          </CardContent>
        </Card>

        <Card className="text-center rounded-none p-8 hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
          <CardHeader className="pb-4">
            <div className="mb-4 text-4xl">👥</div>
            <CardTitle className="text-xl font-semibold mb-2">
              Agent Matching
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 leading-relaxed">
              Connect with qualified immigration agents based on your specific
              needs and destination country
            </p>
          </CardContent>
        </Card>

        <Card className="text-center rounded-none p-8 hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
          <CardHeader className="pb-4">
            <div className="mb-4 text-4xl">📊</div>
            <CardTitle className="text-xl font-semibold mb-2">
              Progress Tracking
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 leading-relaxed">
              Real-time updates on your application status with detailed
              timeline and next steps
            </p>
          </CardContent>
        </Card>

        <Card className="text-center rounded-none p-8 hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
          <CardHeader className="pb-4">
            <div className="mb-4 text-4xl">🔒</div>
            <CardTitle className="text-xl font-semibold mb-2">
              Secure Platform
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 leading-relaxed">
              Bank-level security with encrypted data storage and secure
              communication channels
            </p>
          </CardContent>
        </Card>

        <Card className="text-center rounded-none p-8 hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
          <CardHeader className="pb-4">
            <div className="mb-4 text-4xl">💬</div>
            <CardTitle className="text-xl font-semibold mb-2">
              24/7 Support
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 leading-relaxed">
              Round-the-clock customer support with multilingual assistance for
              global clients
            </p>
          </CardContent>
        </Card>

        <Card className="text-center rounded-none p-8 hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
          <CardHeader className="pb-4">
            <div className="mb-4 text-4xl">⚡</div>
            <CardTitle className="text-xl font-semibold mb-2">
              Fast Processing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 leading-relaxed">
              Streamlined workflows and automated processes to accelerate your
              application timeline
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
