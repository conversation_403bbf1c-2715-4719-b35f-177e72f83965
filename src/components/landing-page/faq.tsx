import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
export default function FAQ() {
  return (
    <div className="mt-32">
      <h2 className="text-4xl font-semibold text-center mb-16">
        Frequently Asked Questions
      </h2>
      <Accordion type="single" collapsible className="w-[80%] mx-auto">
        <AccordionItem value="item-1">
          <AccordionTrigger>
            How does the agent matching process work?
          </AccordionTrigger>
          <AccordionContent>
            Our system matches you with immigration agents based on your
            specific visa requirements, preferred destination country, and other
            relevant criteria to ensure the best fit for your case.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-2">
          <AccordionTrigger>
            What types of visas do you handle?
          </AccordionTrigger>
          <AccordionContent>
            We support various visa types including student visas, work visas,
            permanent residency, family sponsorship, and business visas. Our
            agents are experienced in handling different visa categories.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-3">
          <AccordionTrigger>
            How long does the visa process typically take?
          </AccordionTrigger>
          <AccordionContent>
            Processing times vary depending on the visa type and destination
            country. Your assigned agent will provide you with an estimated
            timeline based on your specific case and current processing times.
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="item-4">
          <AccordionTrigger>
            What documents will I need to provide?
          </AccordionTrigger>
          <AccordionContent>
            Required documents vary by visa type but typically include
            identification, educational certificates, work experience proof, and
            financial documents. Your agent will provide a detailed checklist.
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
