"use client";
import React from "react";
import { Application, ApplicationBoard } from "@/types/application";
import { ApplicationColumn } from "./application-column";
import { useMemo, useState } from "react";
import { DragDropContext, DropResult } from "@hello-pangea/dnd";
import { SearchAndFilter } from "./seaech-filter";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import EmptyList from "@/components/empty-list";
import { LoadingSpinner } from "@/components/loading-spinner";
import { useSession } from "next-auth/react";

const ImmigrationBoard: React.FC = () => {
  const { data: session, status } = useSession();

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["userbaord"],
    queryFn: async () => {
      const res = await axios.get("/api/v1/users/board/application");
      return res.data;
    },
  });

  const [boardData] = useState<ApplicationBoard>({
    applications: {
      "app-1": {
        id: "app-1",
        applicationNumber: "IMM-2024-001",
        applicationType: "work_permit",
        status: "submitted",
        applicant: {
          id: "1",
          firstName: "John",
          lastName: "Smith",
          email: "<EMAIL>",
          phone: "******-0123",
          nationality: { code: "US", name: "United States", flag: "🇺🇸" },
          dateOfBirth: "1985-03-15",
        },
        caseOfficer: {
          id: "officer-1",
          name: "Sarah Johnson",
          email: "<EMAIL>",
          initials: "SJ",
        },
        submittedDate: "2024-01-15",
        lastUpdated: "2024-01-20",
        dueDate: "2024-03-01",
        priority: "high",
        documentsCount: 8,
        notesCount: 3,
        isUrgent: false,
        estimatedProcessingTime: 45,
        currentStage: "Initial Review",
        completionPercentage: 25,
        fees: {
          total: 500,
          paid: 500,
          currency: "USD",
        },
      },
      "app-2": {
        id: "app-2",
        applicationNumber: "IMM-2024-002",
        applicationType: "student_visa",
        status: "under_review",
        applicant: {
          id: "2",
          firstName: "Maria",
          lastName: "Garcia",
          email: "<EMAIL>",
          phone: "******-0124",
          nationality: { code: "MX", name: "Mexico", flag: "🇲🇽" },
          dateOfBirth: "1995-07-22",
        },
        caseOfficer: {
          id: "officer-2",
          name: "Mike Chen",
          email: "<EMAIL>",
          initials: "MC",
        },
        submittedDate: "2024-01-18",
        lastUpdated: "2024-01-22",
        dueDate: "2024-02-28",
        priority: "medium",
        documentsCount: 12,
        notesCount: 1,
        isUrgent: true,
        estimatedProcessingTime: 30,
        currentStage: "Document Verification",
        completionPercentage: 60,
        fees: {
          total: 350,
          paid: 200,
          currency: "USD",
        },
      },
      "app-3": {
        id: "app-3",
        applicationNumber: "IMM-2024-003",
        applicationType: "permanent_residence",
        status: "approved",
        applicant: {
          id: "3",
          firstName: "David",
          lastName: "Brown",
          email: "<EMAIL>",
          phone: "******-0125",
          nationality: { code: "CA", name: "Canada", flag: "🇨🇦" },
          dateOfBirth: "1980-12-10",
        },
        caseOfficer: {
          id: "officer-3",
          name: "Lisa Wang",
          email: "<EMAIL>",
          initials: "LW",
        },
        submittedDate: "2024-01-10",
        lastUpdated: "2024-01-25",
        priority: "urgent",
        documentsCount: 15,
        notesCount: 5,
        isUrgent: false,
        estimatedProcessingTime: 90,
        currentStage: "Approved - Awaiting Card Production",
        completionPercentage: 100,
        fees: {
          total: 1200,
          paid: 1200,
          currency: "USD",
        },
      },
    },
    columns: [
      {
        id: "submitted",
        title: "Submitted",
        status: "submitted",
        applicationIds: ["app-1"],
        color: "#3b82f6",
        order: 1,
      },
      {
        id: "under-review",
        title: "Under Review",
        status: "under_review",
        applicationIds: ["app-2"],
        color: "#f59e0b",
        order: 2,
      },
      {
        id: "documents-requested",
        title: "Documents Requested",
        status: "documents_requested",
        applicationIds: [],
        color: "#8b5cf6",
        order: 3,
      },
      {
        id: "interview-scheduled",
        title: "Interview Scheduled",
        status: "interview_scheduled",
        applicationIds: [],
        color: "#06b6d4",
        order: 4,
      },
      {
        id: "approved",
        title: "Approved",
        status: "approved",
        applicationIds: ["app-3"],
        color: "#10b981",
        order: 5,
      },
      {
        id: "rejected",
        title: "Rejected",
        status: "rejected",
        applicationIds: [],
        color: "#ef4444",
        order: 6,
      },
      {
        id: "pending-payment",
        title: "Pending Payment",
        status: "pending_payment",
        applicationIds: [],
        color: "#f97316",
        order: 7,
      },
      {
        id: "completed",
        title: "Completed",
        status: "completed",
        applicationIds: [],
        color: "#22c55e",
        order: 8,
      },
    ],
    columnOrder: [
      "submitted",
      "under-review",
      "documents-requested",
      "interview-scheduled",
      "approved",
      "rejected",
      "pending-payment",
      "completed",
    ],
  });

  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [filterUrgent, setFilterUrgent] = useState(false);

  // Filter applications based on search and filters
  const filteredApplications = useMemo(() => {
    const filtered: Record<string, Application> = {};

    Object.entries(boardData.applications).forEach(([id, app]) => {
      const matchesSearch =
        searchTerm === "" ||
        `${app.applicant.firstName} ${app.applicant.lastName}`
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        app.applicant.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.applicationNumber
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        app.applicant.nationality.name
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

      const matchesType =
        filterType === "all" || app.applicationType === filterType;
      const matchesPriority =
        filterPriority === "all" || app.priority === filterPriority;
      const matchesUrgent = !filterUrgent || app.isUrgent;

      if (matchesSearch && matchesType && matchesPriority && matchesUrgent) {
        filtered[id] = app;
      }
    });

    return filtered;
  }, [
    boardData.applications,
    searchTerm,
    filterType,
    filterPriority,
    filterUrgent,
  ]);

  const onDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result;

    if (!destination) {
      return;
    }

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    // Handle drag and drop logic here
    console.log(
      "Moving application:",
      draggableId,
      "from",
      source.droppableId,
      "to",
      destination.droppableId
    );
  };

  if (isLoading) return <LoadingSpinner text="Loading your board" />;
  if (status === "loading") return <LoadingSpinner text="Loading your board" />;
  if (!data) return <EmptyList text="Contact Manager to setup your board" />;

  return (
    <div className="h-full">
      <SearchAndFilter
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        filterType={filterType}
        onFilterTypeChange={setFilterType}
        filterPriority={filterPriority}
        onFilterPriorityChange={setFilterPriority}
        filterUrgent={filterUrgent}
        onFilterUrgentChange={setFilterUrgent}
      />

      <DragDropContext onDragEnd={onDragEnd}>
        <div className="flex gap-6  w-full overflow-x-auto  h-full">
          {boardData.columnOrder.map((columnId) => {
            const column = boardData.columns.find((col) => col.id === columnId);
            if (!column) return null;

            const applications = column.applicationIds
              .map((appId) => filteredApplications[appId])
              .filter(Boolean);

            return (
              <ApplicationColumn
                key={column.id}
                column={column}
                applications={applications}
              />
            );
          })}
        </div>
      </DragDropContext>
    </div>
  );
};

export default ImmigrationBoard;
