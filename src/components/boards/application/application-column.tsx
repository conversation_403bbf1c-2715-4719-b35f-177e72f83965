import React from "react";
import {
  Application,
  ApplicationColumn as Column,
  ApplicationStatus,
} from "@/types/application";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Droppable } from "@hello-pangea/dnd";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  ChevronDown,
  FileText,
  Calendar,
  Flag,
  Copy,
  Upload,
  Settings,
  Trash2,
  Filter,
  DollarSign,
  CheckCircle2,
} from "lucide-react";
import { ApplicationCard } from "./application-card";
interface ApplicationColumnProps {
  column: Column;
  applications: Application[];
}
export const ApplicationColumn: React.FC<ApplicationColumnProps> = ({
  column,
  applications,
}) => {
  const getStatusIcon = (status: ApplicationStatus) => {
    switch (status) {
      case "approved":
        return <CheckCircle2 className="w-3 h-3 text-green-600" />;
      case "rejected":
        return <Flag className="w-3 h-3 text-red-600" />;
      case "pending_payment":
        return <DollarSign className="w-3 h-3 text-yellow-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="bg-gray-50 p-4 w-[350px] flex-shrink-0">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: column.color }}
          />
          <h3 className="font-semibold text-sm">{column.title}</h3>
          {getStatusIcon(column.status)}
          <Badge variant="secondary" className="text-xs">
            {applications.length}
          </Badge>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <ChevronDown className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem>
              <Plus className="w-4 h-4 mr-2" />
              Add Application
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Filter className="w-4 h-4 mr-2" />
              Filter Column
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Settings className="w-4 h-4 mr-2" />
              Column Settings
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Droppable droppableId={column.id}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={`min-h-[500px] transition-colors flex-shrink-0 ${
              snapshot.isDraggingOver ? "" : ""
            }`}
          >
            {applications.map((application, index) => (
              <ApplicationCard
                key={application.id}
                application={application}
                index={index}
              />
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );
};
