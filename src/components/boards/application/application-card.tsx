// components/ApplicationCard.tsx
import React from "react";
import { Draggable } from "@hello-pangea/dnd";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge, badgeVariants } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { Progress } from "@/components/ui/progress";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Calendar,
  Clock,
  Flag,
  FileText,
  MessageSquare,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  User,
  CalendarDays,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Globe,
} from "lucide-react";
import {
  Application,
  ApplicationCardProps,
  Priority,
  ApplicationType,
} from "@/types/application";
import { VariantProps } from "class-variance-authority";

const getPriorityVariant = (
  priority: Priority
): VariantProps<typeof badgeVariants>["variant"] => {
  switch (priority) {
    case "urgent":
      return "destructive";
    case "high":
      return "destructive";
    case "medium":
      return "default";
    case "low":
      return "secondary";
    default:
      return "outline";
  }
};

const getApplicationTypeLabel = (type: ApplicationType): string => {
  const labels: Record<ApplicationType, string> = {
    student_visa: "Student Visa",
    work_permit: "Work Permit",
    permanent_residence: "PR",
    citizenship: "Citizenship",
    family_reunion: "Family Reunion",
    refugee_status: "Refugee",
    tourist_visa: "Tourist Visa",
    business_visa: "Business Visa",
  };
  return labels[type];
};

const getApplicationTypeColor = (type: ApplicationType): string => {
  const colors: Record<ApplicationType, string> = {
    student_visa: "bg-blue-100 text-blue-800",
    work_permit: "bg-green-100 text-green-800",
    permanent_residence: "bg-purple-100 text-purple-800",
    citizenship: "bg-yellow-100 text-yellow-800",
    family_reunion: "bg-pink-100 text-pink-800",
    refugee_status: "bg-red-100 text-red-800",
    tourist_visa: "bg-cyan-100 text-cyan-800",
    business_visa: "bg-orange-100 text-orange-800",
  };
  return colors[type];
};

const isOverdue = (dueDate?: string): boolean => {
  if (!dueDate) return false;
  return new Date(dueDate) < new Date();
};

const formatCurrency = (amount: number, currency: string): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
  }).format(amount);
};

export const ApplicationCard: React.FC<ApplicationCardProps> = ({
  application,
  index,
  onUpdate,
  onDelete,
}) => {
  const isPaymentPending = application.fees.paid < application.fees.total;
  const overdue = isOverdue(application.dueDate);

  return (
    <Draggable draggableId={application.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={`mb-3 ${
            snapshot.isDragging ? "rotate-3 scale-105" : ""
          } transition-transform`}
        >
          <Card className="rounded-none shadow-none cursor-pointer">
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <CardTitle className="text-sm font-medium leading-none">
                      #{application.applicationNumber}
                    </CardTitle>
                    {application.isUrgent && (
                      <AlertTriangle className="h-3 w-3 text-red-500" />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      className={`text-xs ${getApplicationTypeColor(
                        application.applicationType
                      )}`}
                      variant="secondary"
                    >
                      {getApplicationTypeLabel(application.applicationType)}
                    </Badge>
                    <Badge
                      variant={getPriorityVariant(application.priority)}
                      className="text-xs"
                    >
                      <Flag className="w-2 h-2 mr-1" />
                      {application.priority}
                    </Badge>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-gray-100"
                    >
                      <MoreHorizontal className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem>
                      <Eye className="w-4 h-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Application
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <User className="w-4 h-4 mr-2" />
                      Reassign Officer
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <CalendarDays className="w-4 h-4 mr-2" />
                      Schedule Interview
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <FileText className="w-4 h-4 mr-2" />
                      Request Documents
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-red-600 focus:text-red-600">
                      <Trash2 className="w-4 h-4 mr-2" />
                      Archive Application
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <CardDescription className="text-xs mt-2">
                <div className="flex items-center gap-1">
                  <span className="text-xs">
                    {application.applicant.nationality.flag}
                  </span>
                  <span>
                    {application.applicant.firstName}{" "}
                    {application.applicant.lastName}
                  </span>
                </div>
              </CardDescription>
            </CardHeader>

            <CardContent className="pt-0 space-y-3">
              {/* Progress bar */}
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{application.currentStage}</span>
                  <span>{application.completionPercentage}%</span>
                </div>
                {/* <Progress
                  value={application.completionPercentage}
                  className="h-1"
                /> */}
              </div>

              {/* Due date and payment status */}
              <div className="flex items-center justify-between text-xs">
                <div
                  className={`flex items-center gap-1 ${
                    overdue ? "text-red-600" : "text-muted-foreground"
                  }`}
                >
                  {overdue && <Clock className="w-3 h-3" />}
                  <Calendar className="w-3 h-3" />
                  <span>
                    {application.dueDate
                      ? new Date(application.dueDate).toLocaleDateString()
                      : `${application.estimatedProcessingTime}d est.`}
                  </span>
                </div>
                {isPaymentPending && (
                  <div className="flex items-center gap-1 text-orange-600">
                    <DollarSign className="w-3 h-3" />
                    <span>Payment Due</span>
                  </div>
                )}
              </div>

              {/* Bottom row */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {application.caseOfficer ? (
                    <>
                      <Avatar className="h-5 w-5">
                        <AvatarImage src={application.caseOfficer.avatar} />
                        <AvatarFallback className="text-xs">
                          {application.caseOfficer.initials}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">
                        {application.caseOfficer.name}
                      </span>
                    </>
                  ) : (
                    <Badge variant="outline" className="text-xs">
                      Unassigned
                    </Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {application.notesCount > 0 && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <MessageSquare className="w-3 h-3" />
                      <span>{application.notesCount}</span>
                    </div>
                  )}
                  {application.documentsCount > 0 && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <FileText className="w-3 h-3" />
                      <span>{application.documentsCount}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Fees info */}
              <div className="flex justify-between items-center text-xs">
                <span className="text-muted-foreground">
                  Fees:{" "}
                  {formatCurrency(
                    application.fees.paid,
                    application.fees.currency
                  )}{" "}
                  /{" "}
                  {formatCurrency(
                    application.fees.total,
                    application.fees.currency
                  )}
                </span>
                {application.fees.paid >= application.fees.total && (
                  <CheckCircle className="w-3 h-3 text-green-500" />
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </Draggable>
  );
};
