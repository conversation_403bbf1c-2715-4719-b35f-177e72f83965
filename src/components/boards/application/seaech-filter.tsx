import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { AlertTriangle, Search } from "lucide-react";

interface SearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filterType: string;
  onFilterTypeChange: (type: string) => void;
  filterPriority: string;
  onFilterPriorityChange: (priority: string) => void;
  filterUrgent: boolean;
  onFilterUrgentChange: (urgent: boolean) => void;
}

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  searchTerm,
  onSearchChange,
  filterType,
  onFilterTypeChange,
  filterPriority,
  onFilterPriorityChange,
  filterUrgent,
  onFilterUrgentChange,
}) => {
  return (
    <div className="flex items-center gap-4 mb-6 p-4 bg-white rounded-lg shadow-sm flex-shrink-0">
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search applications..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>

      <Select value={filterType} onValueChange={onFilterTypeChange}>
        <SelectTrigger className="w-[180px] rounded-none shadow-none">
          <SelectValue placeholder="Application Type" />
        </SelectTrigger>
        <SelectContent className="rounded-none">
          <SelectItem value="all">All Types</SelectItem>
          <SelectItem value="work_permit">Work Permit</SelectItem>
          <SelectItem value="student_visa">Student Visa</SelectItem>
          <SelectItem value="tourist_visa">Tourist Visa</SelectItem>
          <SelectItem value="business_visa">Business Visa</SelectItem>
          <SelectItem value="permanent_residence">
            Permanent Residence
          </SelectItem>
          <SelectItem value="citizenship">Citizenship</SelectItem>
          <SelectItem value="family_reunion">Family Reunion</SelectItem>
          <SelectItem value="refugee_status">Refugee Status</SelectItem>
        </SelectContent>
      </Select>

      <Select value={filterPriority} onValueChange={onFilterPriorityChange}>
        <SelectTrigger className="w-[140px] rounded-none shadow-none">
          <SelectValue placeholder="Priority" />
        </SelectTrigger>
        <SelectContent className="rounded-none">
          <SelectItem value="all">All Priorities</SelectItem>
          <SelectItem value="urgent">Urgent</SelectItem>
          <SelectItem value="high">High</SelectItem>
          <SelectItem value="medium">Medium</SelectItem>
          <SelectItem value="low">Low</SelectItem>
        </SelectContent>
      </Select>

      <Button
        variant={filterUrgent ? "default" : "outline"}
        size="sm"
        onClick={() => onFilterUrgentChange(!filterUrgent)}
        className="flex items-center gap-2 rounded-none shadow-none"
      >
        <AlertTriangle className="h-3 w-3" />
        Urgent Only
      </Button>
    </div>
  );
};
