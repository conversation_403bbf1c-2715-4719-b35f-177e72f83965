"use client";
import React, { useState, useEffect } from "react";
import { DragDropContext, DropResult } from "@hello-pangea/dnd";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { Column as TaskColumn } from "./task-column";
import { useSession } from "next-auth/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { LoadingSpinner } from "@/components/loading-spinner";
import EmptyList from "@/components/empty-list";
import { toast } from "sonner";

const UsersTaskBoard: React.FC = () => {
  const queryClient = useQueryClient();
  const { data: session, status } = useSession();
  const [isAddColumnOpen, setIsAddColumnOpen] = useState<boolean>(false);
  const [newColumnTitle, setNewColumnTitle] = useState<string>("");
  const [columns, setColumns] = useState<any>({});

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["userbaord"],
    queryFn: async () => {
      const res = await axios.get("/api/v1/users/board/tasks");
      return res.data;
    },
  });

  useEffect(() => {
    if (data?.columns) {
      const columnsMap = data.columns.reduce((acc: any, col: any) => {
        acc[col.id] = col;
        return acc;
      }, {});
      setColumns(columnsMap);
    }
  }, [data]);

  const { mutate: createBoard, isPending: isCreatingBoard } = useMutation({
    mutationFn: async () => {
      const res = await axios.post("/api/v1/users/board/tasks");
      return res.data;
    },
    onSuccess: () => {
      // Invalidate the query to refetch the data
      toast.success("Task board created successfully");
      queryClient.invalidateQueries({ queryKey: ["userbaord"] });
    },
    onError: () => {
      toast.error("Failed to create task board");
    },
  });

  const { mutate: addColumn, isPending: isAddingColumn } = useMutation({
    mutationFn: async (title: string) => {
      const res = await axios.post("/api/v1/users/board/tasks/columns", {
        title,
      });
      return res.data;
    },
    onSuccess: () => {
      toast.success("Column added successfully");
      queryClient.invalidateQueries({ queryKey: ["userbaord"] });
      setIsAddColumnOpen(false);
      setNewColumnTitle("");
    },
    onError: () => {
      toast.error("Failed to add column");
    },
  });

  if (isLoading) return <LoadingSpinner text="Loading..." />;
  if (isError) return <div>{error.message}</div>;

  if (!data)
    return (
      <EmptyList
        text="No tasks found"
        buttonText="Create Task Board"
        isLoading={isCreatingBoard}
        onClick={() => createBoard()}
      />
    );

  // Initialize columns state from data

  const taskBoardData = data;

  const onDragEnd = (result: DropResult): void => {
    const { destination, source, draggableId } = result;

    if (!destination) return;

    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    const start = columns[source.droppableId];
    const finish = columns[destination.droppableId];

    if (start === finish) {
      const newTaskIds = Array.from(start.taskIds);
      newTaskIds.splice(source.index, 1);
      newTaskIds.splice(destination.index, 0, draggableId);

      const newColumn = {
        ...start,
        taskIds: newTaskIds,
      };

      setColumns({
        ...columns,
        [newColumn.id]: newColumn,
      });
      return;
    }

    const startTaskIds = Array.from(start.taskIds);
    startTaskIds.splice(source.index, 1);
    const newStart = {
      ...start,
      taskIds: startTaskIds,
    };

    const finishTaskIds = Array.from(finish.taskIds);
    finishTaskIds.splice(destination.index, 0, draggableId);
    const newFinish = {
      ...finish,
      taskIds: finishTaskIds,
    };

    setColumns({
      ...columns,
      [newStart.id]: newStart,
      [newFinish.id]: newFinish,
    });
  };

  const addNewTask = (columnId: string): void => {
    // This will be handled by individual column components
    console.log("Add new task to column:", columnId);
  };

  const addNewColumn = (): void => {
    if (newColumnTitle.trim()) {
      addColumn(newColumnTitle.trim());
    }
  };

  const deleteColumn = (columnId: string): void => {
    // Implementation for deleting columns
    console.log("Delete column:", columnId);
  };

  return (
    <div className="">
      {/* Board Content */}
      <div className="flex-1 overflow-x-auto p-6 pr-8 scrollbar-hide">
        <DragDropContext onDragEnd={onDragEnd}>
          <div className="flex gap-6 pb-4 items-start">
            {data.columns.map((item) => {
              return (
                <TaskColumn
                  key={item.id}
                  column={item}
                  onDeleteColumn={deleteColumn}
                />
              );
            })}

            {/* Add New Column */}
            <div className="w-[320px] flex-shrink-0 mr-6">
              <div className="bg-white rounded-lg border border-slate-200 shadow-sm p-4">
                <Dialog
                  open={isAddColumnOpen}
                  onOpenChange={setIsAddColumnOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full h-12 border-2 border-dashed border-slate-300 hover:border-slate-400 hover:bg-slate-50 text-slate-600 rounded-lg transition-colors"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Column
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>Add New Column</DialogTitle>
                      <DialogDescription>
                        Create a new column for your kanban board.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <label
                          htmlFor="column-title"
                          className="text-right text-sm font-medium"
                        >
                          Title
                        </label>
                        <Input
                          id="column-title"
                          value={newColumnTitle}
                          onChange={(e) => setNewColumnTitle(e.target.value)}
                          placeholder="Enter column title..."
                          className="col-span-3"
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              addNewColumn();
                            }
                          }}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setIsAddColumnOpen(false);
                          setNewColumnTitle("");
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="button"
                        onClick={addNewColumn}
                        disabled={!newColumnTitle.trim() || isAddingColumn}
                      >
                        {isAddingColumn ? "Adding..." : "Add Column"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>
        </DragDropContext>
      </div>
    </div>
  );
};

export default UsersTaskBoard;
