import React from "react";
import { ColumnComponentProps } from "@/types/task";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Droppable } from "@hello-pangea/dnd";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  ChevronDown,
  FileText,
  Calendar,
  Flag,
  Copy,
  Upload,
  Settings,
  Trash2,
} from "lucide-react";
import { TaskCard } from "./task-card";
import { toast } from "sonner";

export const Column: React.FC<ColumnComponentProps> = ({
  column,
  onDeleteColumn,
}) => {
  const handleAddNewTask = () => {
    toast.info("Add new task functionality coming soon!");
  };

  const handleAddFromTemplate = () => {
    toast.info("Template functionality coming soon!");
  };

  const handleImportTasks = () => {
    toast.info("Import tasks functionality coming soon!");
  };

  const handleUploadFromFile = () => {
    toast.info("Upload from file functionality coming soon!");
  };

  const handleAddTaskWithDueDate = () => {
    toast.info("Add task with due date functionality coming soon!");
  };

  const handleAddHighPriorityTask = () => {
    toast.info("Add high priority task functionality coming soon!");
  };

  const handleColumnSettings = () => {
    toast.info("Column settings functionality coming soon!");
  };

  const handleDeleteColumn = () => {
    if (column.tasks.length > 0) {
      toast.error(
        "Cannot delete column with tasks. Please move or delete all tasks first."
      );
      return;
    }
    toast.info("Delete column functionality coming soon!");
    // onDeleteColumn(column.id);
  };

  return (
    <div className="bg-white rounded-lg border border-slate-200 w-[320px] flex-shrink-0 shadow-sm flex flex-col">
      <div className="p-4 border-b border-slate-100 flex-shrink-0">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-3">
            <h2 className="font-semibold text-sm text-slate-700 uppercase tracking-wide">
              {column.title}
            </h2>
            <Badge
              variant="secondary"
              className="text-xs bg-slate-100 text-slate-600 hover:bg-slate-200"
            >
              {column.tasks.length}
            </Badge>
          </div>
          <div className="flex items-center gap-1">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0 hover:bg-slate-100 text-slate-500"
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem onClick={handleAddNewTask}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Task
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleAddFromTemplate}>
                  <FileText className="w-4 h-4 mr-2" />
                  Add from Template
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleImportTasks}>
                  <Copy className="w-4 h-4 mr-2" />
                  Import Tasks
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleUploadFromFile}>
                  <Upload className="w-4 h-4 mr-2" />
                  Upload from File
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleAddTaskWithDueDate}>
                  <Calendar className="w-4 h-4 mr-2" />
                  Add Task with Due Date
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleAddHighPriorityTask}>
                  <Flag className="w-4 h-4 mr-2" />
                  Add High Priority Task
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleColumnSettings}>
                  <Settings className="w-4 h-4 mr-2" />
                  Column Settings
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="text-red-600 focus:text-red-600"
                  onClick={handleDeleteColumn}
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Column
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <div className="p-4 flex-1 flex flex-col">
        <Droppable droppableId={column.id}>
          {(provided, snapshot) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={`flex-1 min-h-[200px] transition-all duration-200 rounded-lg ${
                snapshot.isDraggingOver
                  ? "bg-blue-50 border-2 border-dashed border-blue-300"
                  : "bg-transparent"
              }`}
            >
              {column.tasks?.map((task, index) => (
                <TaskCard key={task.id} task={task} index={index} />
              ))}
              {provided.placeholder}

              <div className="mt-auto pt-3">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full border-2 border-dashed border-slate-300 hover:border-slate-400 hover:bg-slate-50 h-10 text-slate-600 rounded-lg"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add a task
                      <ChevronDown className="h-3 w-3 ml-auto" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="center" className="w-56">
                    <DropdownMenuItem onClick={handleAddNewTask}>
                      <Plus className="w-4 h-4 mr-2" />
                      Create New Task
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleAddFromTemplate}>
                      <FileText className="w-4 h-4 mr-2" />
                      Use Template
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleAddTaskWithDueDate}>
                      <Calendar className="w-4 h-4 mr-2" />
                      Quick Task with Due Date
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleAddHighPriorityTask}>
                      <Flag className="w-4 h-4 mr-2" />
                      High Priority Task
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() =>
                        toast.info(
                          "Paste from clipboard functionality coming soon!"
                        )
                      }
                    >
                      <Copy className="w-4 h-4 mr-2" />
                      Paste from Clipboard
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleUploadFromFile}>
                      <Upload className="w-4 h-4 mr-2" />
                      Import from File
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          )}
        </Droppable>
      </div>
    </div>
  );
};
