import { Draggable } from "@hello-pangea/dnd";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge, badgeVariants } from "@/components/ui/badge";
import moment from "moment";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Calendar,
  Clock,
  Flag,
  MessageSquare,
  Paperclip,
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  User,
  CalendarDays,
  Archive,
  Eye,
} from "lucide-react";
import { Task, TaskCardProps } from "@/types/task";
import { VariantProps } from "class-variance-authority";

const getPriorityVariant = (
  priority: Task["priority"]
): VariantProps<typeof badgeVariants>["variant"] => {
  switch (priority) {
    case "high":
      return "destructive";
    case "medium":
      return "default";
    case "low":
      return "secondary";
    default:
      return "outline";
  }
};

const getPriorityColor = (priority: Task["priority"]): string => {
  switch (priority) {
    case "high":
      return "text-red-600 bg-red-50 border-red-200";
    case "medium":
      return "text-orange-600 bg-orange-50 border-orange-200";
    case "low":
      return "text-green-600 bg-green-50 border-green-200";
    default:
      return "text-slate-600 bg-slate-50 border-slate-200";
  }
};

const isOverdue = (dueDate: string): boolean => {
  return new Date(dueDate) < new Date();
};

export const TaskCard: React.FC<TaskCardProps> = ({ task, index }) => (
  <Draggable draggableId={task.id} index={index}>
    {(provided, snapshot) => (
      <div
        ref={provided.innerRef}
        {...provided.draggableProps}
        {...provided.dragHandleProps}
        className={` ${
          snapshot.isDragging
            ? "rotate-2 scale-105 shadow-lg"
            : "hover:shadow-md"
        } transition-all duration-200`}
      >
        <Card className="group p-3 border-none  rounded-sm  shadow-sm shadow-gray-100 cursor-pointer  bg-white">
          <CardHeader className="px-0">
            <div className="flex items-start justify-between">
              <CardTitle className="text-sm font-medium leading-tight text-slate-800 pr-2">
                {task.title}
              </CardTitle>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 hover:bg-slate-100 text-slate-400 hover:text-slate-600 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem>
                    <Eye className="w-4 h-4 mr-2" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Task
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Copy className="w-4 h-4 mr-2" />
                    Duplicate Task
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <User className="w-4 h-4 mr-2" />
                    Change Assignee
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <CalendarDays className="w-4 h-4 mr-2" />
                    Change Due Date
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Flag className="w-4 h-4 mr-2" />
                    Change Priority
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Archive className="w-4 h-4 mr-2" />
                    Archive Task
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600 focus:text-red-600">
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Task
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            {task.description && (
              <CardDescription className="text-xs  line-clamp-2 text-slate-600">
                {task.description}
              </CardDescription>
            )}
          </CardHeader>
          <CardContent className="px-4 pt-0">
            <div className="flex items-center justify-between">
              <div
                className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${getPriorityColor(
                  task.priority
                )}`}
              >
                {task.priority?.toUpperCase()}
              </div>
              {task.dueDate && (
                <div
                  className={`flex items-center gap-1 text-xs ${
                    isOverdue(task.dueDate)
                      ? "text-red-600 font-medium"
                      : "text-slate-500"
                  }`}
                >
                  <Calendar className="w-3 h-3" />
                  <span>{moment(task.dueDate).format("MMM D")}</span>
                </div>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {task.assignee && (
                  <>
                    <Avatar className="h-6 w-6 border border-slate-200">
                      <AvatarImage src={task.assignee.avatar || undefined} />
                      <AvatarFallback className="text-xs bg-slate-100 text-slate-600">
                        {task.assignee.initials}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs text-slate-600 font-medium">
                      {task.assignee.name}
                    </span>
                  </>
                )}
              </div>
              <div className="flex items-center gap-3">
                {task.comments > 0 && (
                  <div className="flex items-center gap-1 text-xs text-slate-500 hover:text-slate-700">
                    <MessageSquare className="w-3 h-3" />
                    <span>{task.comments}</span>
                  </div>
                )}
                {task.attachments > 0 && (
                  <div className="flex items-center gap-1 text-xs text-slate-500 hover:text-slate-700">
                    <Paperclip className="w-3 h-3" />
                    <span>{task.attachments}</span>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )}
  </Draggable>
);
