/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import { Field, FieldOption } from "@/types/forms";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Plus, Minus } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getSources } from "@/actions/sources";
import { CountryRes } from "@/types/country";

interface FormPreviewProps {
  title: string;
  description?: string;
  fields: Field[];
  country: CountryRes;
  onSubmit: (data: Record<string, any>) => void;
  isSubmitting?: boolean;
  type: "apply" | "register" | "view";
}

const FormPreview: React.FC<FormPreviewProps> = ({
  title,
  description,
  fields,
  onSubmit,
  country,
  isSubmitting = false,
  type,
}) => {
  const { data: sources, isLoading: isSourcesLoading } = useQuery({
    queryKey: ["sources"],
    queryFn: getSources,
  });
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [arrayItems, setArrayItems] = useState<Record<string, number[]>>({});
  const [source, setSource] = useState<string | undefined>(undefined);
  const [errorMessage, seterrorMessage] = useState("");

  // Initialize array fields
  useEffect(() => {
    const initialArrayItems: Record<string, number[]> = {};
    fields.forEach((field) => {
      if (field.type === "array" || field.isArray) {
        const minItems = field.arrayConfig?.minItems || 1;
        initialArrayItems[field.key] = Array.from(
          { length: minItems },
          (_, i) => i
        );
      }
    });
    setArrayItems(initialArrayItems);
  }, [fields]);

  const handleInputChange = (fieldId: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [fieldId]: value,
    }));
  };

  const handleArrayInputChange = (
    fieldId: string,
    index: number,
    value: any
  ) => {
    setFormData((prev) => {
      const currentArray = Array.isArray(prev[fieldId])
        ? [...prev[fieldId]]
        : [];
      currentArray[index] = value;
      return {
        ...prev,
        [fieldId]: currentArray,
      };
    });
  };

  const addArrayItem = (fieldId: string) => {
    const field = fields.find((f) => f.key === fieldId);
    if (!field) return;

    const maxItems = field.arrayConfig?.maxItems || null;
    const currentItems = arrayItems[fieldId] || [];

    if (maxItems === null || currentItems.length < maxItems) {
      setArrayItems((prev) => ({
        ...prev,
        [fieldId]: [...(prev[fieldId] || []), (prev[fieldId] || []).length],
      }));
    }
  };

  const removeArrayItem = (fieldId: string, index: number) => {
    const field = fields.find((f) => f.key === fieldId);
    if (!field) return;

    const minItems = field.arrayConfig?.minItems || 1;
    const currentItems = arrayItems[fieldId] || [];

    if (currentItems.length > minItems) {
      setArrayItems((prev) => ({
        ...prev,
        [fieldId]: prev[fieldId].filter((_, i) => i !== index),
      }));

      // Also update form data
      setFormData((prev) => {
        if (!Array.isArray(prev[fieldId])) return prev;

        const newArray = [...prev[fieldId]];
        newArray.splice(index, 1);
        return {
          ...prev,
          [fieldId]: newArray,
        };
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!source) {
      seterrorMessage("Please select a source");
      return;
    }
    formData.source = source;
    onSubmit(formData);
  };

  const renderField = (field: Field) => {
    switch (field.type) {
      case "textarea":
        return (
          <Textarea
            id={field.id}
            placeholder={field.placeholder}
            value={formData[field.key] || ""}
            onChange={(e) => handleInputChange(field.key, e.target.value)}
          />
        );

      case "select":
      case "multiselect":
        return (
          <select
            id={field.id}
            value={formData[field.key] || ""}
            onChange={(e) => handleInputChange(field.key, e.target.value)}
            multiple={field.type === "multiselect"}
            className="w-full px-3 py-2 border rounded-md"
          >
            <option value="">Select an option</option>
            {field.options?.map((option: FieldOption) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case "radio":
        return (
          <div className="space-y-2">
            {field.options?.map((option: FieldOption) => (
              <div key={option.value} className="flex items-center">
                <input
                  type="radio"
                  id={`${field.id}_${option.value}`}
                  name={field.key}
                  value={option.value}
                  checked={formData[field.key] === option.value}
                  onChange={() => handleInputChange(field.key, option.value)}
                  className="mr-2"
                />
                <Label htmlFor={`${field.key}_${option.value}`}>
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      case "checkbox":
        return (
          <div className="space-y-2">
            {field.options?.map((option: FieldOption) => (
              <div key={option.value} className="flex items-center">
                <Checkbox
                  id={`${field.id}_${option.value}`}
                  checked={(formData[field.key] || []).includes(option.value)}
                  onCheckedChange={(checked) => {
                    const values = new Set(formData[field.key] || []);
                    if (checked) {
                      values.add(option.value);
                    } else {
                      values.delete(option.value);
                    }
                    handleInputChange(field.key, Array.from(values));
                  }}
                  className="mr-2"
                />
                <Label htmlFor={`${field.id}_${option.value}`}>
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        );

      case "date":
      case "time":
      case "datetime":
        return (
          <Input
            id={field.id}
            type={field.type === "datetime" ? "datetime-local" : field.type}
            value={formData[field.key] || ""}
            onChange={(e) => handleInputChange(field.key, e.target.value)}
          />
        );

      case "file":
        return (
          <Input
            id={field.id}
            type="file"
            onChange={(e) => {
              const file = (e.target as HTMLInputElement).files?.[0];
              if (file) {
                handleInputChange(field.key, file);
              }
            }}
          />
        );

      case "array":
        return (
          <div className="space-y-3 pl-4 border-l-2 border-blue-200">
            {(arrayItems[field.id] || []).map((itemIndex) => (
              <div
                key={`${field.id}_${itemIndex}`}
                className="flex items-start gap-2"
              >
                <div className="flex-1">
                  <Input
                    type={
                      field.arrayConfig?.itemType === "number"
                        ? "number"
                        : "text"
                    }
                    placeholder={`${field.arrayConfig?.itemLabel || "Item"} ${
                      itemIndex + 1
                    }`}
                    value={(formData[field.key] || [])[itemIndex] || ""}
                    onChange={(e) =>
                      handleArrayInputChange(
                        field.key,
                        itemIndex,
                        e.target.value
                      )
                    }
                  />
                </div>
                {(arrayItems[field.id]?.length || 0) >
                  (field.arrayConfig?.minItems || 1) && (
                  <button
                    type="button"
                    onClick={() => removeArrayItem(field.key, itemIndex)}
                    className="text-red-500 hover:text-red-700 p-1"
                  >
                    <Minus size={14} />
                  </button>
                )}
              </div>
            ))}

            {(field.arrayConfig?.maxItems === null ||
              (arrayItems[field.id]?.length || 0) <
                (field.arrayConfig?.maxItems || 10)) && (
              <button
                type="button"
                onClick={() => addArrayItem(field.id)}
                className="flex items-center text-sm text-blue-500 hover:text-blue-700"
              >
                <Plus size={14} className="mr-1" />
                Add {field.arrayConfig?.itemLabel || "Item"}
              </button>
            )}
          </div>
        );

      default:
        return (
          <Input
            id={field.id}
            type={field.type}
            placeholder={field.placeholder}
            value={formData[field.key] || ""}
            onChange={(e) => handleInputChange(field.key, e.target.value)}
          />
        );
    }
  };

  return (
    <div className=" flex justify-center p-6 bg-gray-50 min-h-screen">
      <div className="flex bg-white justify-center h-full  flex-col md:w-[50%] py-10">
        <div className="md:mt-30 flex  items-center flex-col gap-2">
          {/* <div className="h-20 w-20  bg-primary rounded"></div> */}
          <img src={country.flag} className="w-10" />
          <h1 className="text-2xl font-bold text-center">
            {title} for {country.name}{" "}
          </h1>
          {description && (
            <p className="text-gray-600 mb-12 text-center max-w-[700px] leading-7">
              {description}
            </p>
          )}
        </div>

        {/* {isSourcesLoading && <Spinner />}
      {!isSourcesLoading && !sources?.data?.length && (
        <ErrorMessage message="No sources available. Please Contact Manager to add sources." />
      )} */}

        <form onSubmit={handleSubmit} className="space-y-6 w-[500px] mx-auto">
          <div className="border-l-4 border-e-primary">
            <div className="px-4">
              <h3 className="text-lg font-medium">Instructions</h3>
              <ul className="list-disc pl-5 mt-2 text-xs text-gray-600">
                <li>Please fill out all required fields marked with *</li>
                <li>Make sure to provide accurate information</li>
                <li>Double check your entries before submitting</li>
              </ul>
            </div>
          </div>
          {sources?.length && (
            <div className="mb-4">
              <Label htmlFor="source">How did you hear about us?</Label>
              <select
                id="source"
                // required
                className="w-full px-3 py-2 border rounded-md"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                disabled={isSourcesLoading}
              >
                <option value="">Select from source...</option>
                {sources?.map((source: any) => (
                  <option key={source.id} value={source.id}>
                    {source.name}
                  </option>
                ))}
              </select>
            </div>
          )}
          {fields.map((field) => (
            <div key={field.id} className="space-y-2">
              <Label htmlFor={field.id}>
                {field.label}{" "}
                {field.required && <span className="text-red-500">*</span>}
              </Label>

              {field.helpText && (
                <p className="text-sm text-gray-500">{field.helpText}</p>
              )}

              {renderField(field)}
            </div>
          ))}

          {/* {errorMessage && <ErrorMessage message={errorMessage} />} */}
          {type === "apply" && (
            <>
              <Button
                type="submit"
                className="mt-6  shadow-lg shadow-primary/20"
                disabled={isSubmitting || !source || source?.length === 0}
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
              <div className="mt-4 text-xs text-gray-500">
                <p>
                  * By submitting this form, you agree to our terms and
                  conditions
                </p>
                <p>* All submitted information will be kept confidential</p>
                <p>* You will receive a confirmation email after submission</p>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default FormPreview;
