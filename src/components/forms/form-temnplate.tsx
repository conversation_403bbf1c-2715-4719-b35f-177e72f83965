/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import type React from "react";
import { useState, useEffect, useCallback } from "react";
import { Move, Copy, Trash2, PlusCircle, Plus, Minus } from "lucide-react";
import dynamic from "next/dynamic";
import type { ArrayFieldItem, Field, FieldType } from "@/types/forms";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectContent,
} from "@/components/ui/select";
import {
  Sidebar,
  SidebarContent,
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { LoadingSpinner } from "../loading-spinner";

// Dynamically import DnD components with SSR disabled to prevent hydration errors
const DragDropContextClient = dynamic(
  () => import("@hello-pangea/dnd").then((mod) => mod.DragDropContext),
  {
    ssr: false,
  }
);
const DroppableClient = dynamic(
  () => import("@hello-pangea/dnd").then((mod) => mod.Droppable),
  { ssr: false }
);
const DraggableClient = dynamic(
  () => import("@hello-pangea/dnd").then((mod) => mod.Draggable),
  { ssr: false }
);

const generateUniqueKey = (label: string, existingKeys: string[] = []) => {
  const baseKey = label
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "");
  if (!baseKey) return "field";

  let uniqueKey = baseKey;
  let counter = 1;

  while (existingKeys.includes(uniqueKey)) {
    uniqueKey = `${baseKey}-${counter}`;
    counter++;
  }

  return uniqueKey;
};

const FIELD_TYPES: { value: FieldType; label: string }[] = [
  { value: "text", label: "Text" },
  { value: "number", label: "Number" },
  { value: "email", label: "Email" },
  { value: "textarea", label: "Text Area" },
  { value: "select", label: "Select" },
  { value: "multiselect", label: "Multi Select" },
  { value: "radio", label: "Radio" },
  { value: "checkbox", label: "Checkbox" },
  { value: "date", label: "Date" },
  { value: "time", label: "Time" },
  { value: "datetime", label: "Date Time" },
  { value: "file", label: "File Upload" },
  { value: "array", label: "Array Field" },
];

const getEmptyArrayItem = (label: string): ArrayFieldItem => ({
  id: `item-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
  title: label,
  type: "text",
  key: generateUniqueKey(label),
  required: false,
  placeholder: "",
  helpText: "",
  options: [],
  validation: {},
  defaultValue: null,
});

const getEmptyField = (order: number): Field => ({
  id: `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
  label: "Untitled Field",
  type: "text",
  key: `field-${order + 1}`,
  required: false,
  placeholder: "",
  helpText: "",
  options: [],
  order,
  isArray: false,
  arrayConfig: {
    minItems: 0,
    maxItems: null,
    itemLabel: "Item",
    items: [getEmptyArrayItem("Item 1"), getEmptyArrayItem("Item 2")],
    itemType: "text",
  },
  validation: {},
  defaultValue: null,
});

interface FormBuilderProps {
  initialFields?: Field[];
  saveFormValue: any;
  countries?: any[];
  selectedCountry: string | null;
  setSelectedCountry?: any;
}

interface FieldOptionsProps {
  field: Field;
  updateField: (id: string, updates: Partial<Field>) => void;
}

const FieldOptions: React.FC<FieldOptionsProps> = ({ field, updateField }) => {
  const [newOption, setNewOption] = useState<string>("");

  const addOption = () => {
    if (!newOption.trim()) return;

    const options = Array.isArray(field.options) ? field.options : [];
    updateField(field.id, {
      options: [
        ...options,
        {
          label: newOption,
          value: newOption.toLowerCase().replace(/\s+/g, "-"),
        },
      ],
    });
    setNewOption("");
  };

  const removeOption = (index: number) => {
    const options = [...field.options];
    options.splice(index, 1);
    updateField(field.id, { options });
  };

  const updateOption = (index: number, value: string) => {
    const options = [...field.options];
    options[index] = {
      ...options[index],
      label: value,
      value: value.toLowerCase().replace(/\s+/g, "-"),
    };
    updateField(field.id, { options });
  };

  return (
    <div className="mt-4  p-3 bg-gray-50">
      <h4 className="text-sm font-medium mb-2">Options</h4>

      {(field.options || []).map((option, index) => (
        <div key={index} className="flex items-center mb-2 gap-2">
          <Input
            type="text"
            value={option.label}
            onChange={(e) => updateOption(index, e.target.value)}
            className="flex-1 text-sm"
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => removeOption(index)}
            className="text-red-500 hover:text-red-700 p-1"
          >
            <Trash2 size={16} />
          </Button>
        </div>
      ))}

      <div className="flex items-center mt-2 gap-2">
        <Input
          type="text"
          value={newOption}
          onChange={(e) => setNewOption(e.target.value)}
          placeholder="Add option"
          className="flex-1 text-sm"
          onKeyDown={(e) => e.key === "Enter" && addOption()}
        />
        <Button onClick={addOption} size="sm" className="px-3">
          Add
        </Button>
      </div>
    </div>
  );
};

// Array field configuration component
const ArrayFieldConfig: React.FC<FieldOptionsProps> = ({
  field,
  updateField,
}) => {
  const addItem = () => {
    if (
      field.arrayConfig.maxItems !== null &&
      field.arrayConfig.items.length >= field.arrayConfig.maxItems
    ) {
      return;
    }

    const newItem = getEmptyArrayItem(
      `${field.arrayConfig.itemLabel} ${field.arrayConfig.items.length + 1}`
    );

    updateField(field.id, {
      arrayConfig: {
        ...field.arrayConfig,
        items: [...field.arrayConfig.items, newItem],
      },
    });
  };

  const updateItem = (itemId: string, updates: Partial<ArrayFieldItem>) => {
    updateField(field.id, {
      arrayConfig: {
        ...field.arrayConfig,
        items: field.arrayConfig.items.map((item) =>
          item.id === itemId ? { ...item, ...updates } : item
        ),
      },
    });
  };

  const removeItem = (itemId: string) => {
    if (field.arrayConfig.items.length <= field.arrayConfig.minItems) {
      return;
    }

    updateField(field.id, {
      arrayConfig: {
        ...field.arrayConfig,
        items: field.arrayConfig.items.filter((item) => item.id !== itemId),
      },
    });
  };

  return (
    <div className="mt-4 border rounded-md p-3 bg-gray-50">
      <h4 className="text-sm font-medium mb-2">Array Field Configuration</h4>

      <div className="space-y-3 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Item Label
          </label>
          <Input
            type="text"
            value={field.arrayConfig.itemLabel}
            onChange={(e) =>
              updateField(field.id, {
                arrayConfig: {
                  ...field.arrayConfig,
                  itemLabel: e.target.value,
                },
              })
            }
            placeholder="Item Label"
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Min Items
            </label>
            <Input
              type="number"
              value={field.arrayConfig.minItems}
              onChange={(e) =>
                updateField(field.id, {
                  arrayConfig: {
                    ...field.arrayConfig,
                    minItems: Math.max(0, Number.parseInt(e.target.value) || 0),
                  },
                })
              }
              min="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Max Items
            </label>
            <Input
              type="number"
              value={
                field.arrayConfig.maxItems === null
                  ? ""
                  : field.arrayConfig.maxItems
              }
              onChange={(e) =>
                updateField(field.id, {
                  arrayConfig: {
                    ...field.arrayConfig,
                    maxItems:
                      e.target.value === ""
                        ? null
                        : Math.max(0, Number.parseInt(e.target.value) || 0),
                  },
                })
              }
              min="0"
              placeholder="No limit"
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h5 className="text-sm font-medium text-gray-700">Array Items</h5>
        {field.arrayConfig.items.map((item) => (
          <ArrayItemConfig
            key={item.id}
            item={item}
            itemLabel={field.arrayConfig.itemLabel}
            onUpdate={updateItem}
            onRemove={removeItem}
            isRemovable={
              field.arrayConfig.items.length > field.arrayConfig.minItems
            }
          />
        ))}

        {(field.arrayConfig.maxItems === null ||
          field.arrayConfig.items.length < field.arrayConfig.maxItems) && (
          <Button
            variant="ghost"
            onClick={addItem}
            className="flex items-center text-sm text-blue-500 hover:text-blue-700"
          >
            <Plus size={14} className="mr-1" />
            Add {field.arrayConfig.itemLabel}
          </Button>
        )}
      </div>
    </div>
  );
};

// Individual array item configuration component
const ArrayItemConfig: React.FC<{
  item: ArrayFieldItem;
  itemLabel: string;
  onUpdate: (itemId: string, updates: Partial<ArrayFieldItem>) => void;
  onRemove: (itemId: string) => void;
  isRemovable: boolean;
}> = ({ item, itemLabel, onUpdate, onRemove, isRemovable }) => {
  return (
    <div className="border rounded-md p-3 mb-3 bg-white">
      <div className="flex items-center justify-between mb-3 gap-2">
        <Input
          type="text"
          value={item.title}
          onChange={(e) => onUpdate(item.id, { title: e.target.value })}
          className="flex-1 text-sm"
          placeholder={`${itemLabel} Title`}
        />
        {isRemovable && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemove(item.id)}
            className="text-red-500 hover:text-red-700 p-1"
          >
            <Trash2 size={16} />
          </Button>
        )}
      </div>

      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Field Type
          </label>
          <Select
            value={item.type}
            onValueChange={(value) =>
              onUpdate(item.id, {
                type: value as FieldType,
                options: [
                  "select",
                  "multiselect",
                  "radio",
                  "checkbox",
                ].includes(value)
                  ? []
                  : undefined,
              })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {FIELD_TYPES.filter((t) => t.value !== "array").map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Placeholder
          </label>
          <Input
            type="text"
            value={item.placeholder}
            onChange={(e) => onUpdate(item.id, { placeholder: e.target.value })}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Help Text
          </label>
          <Input
            type="text"
            value={item.helpText}
            onChange={(e) => onUpdate(item.id, { helpText: e.target.value })}
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            checked={item.required}
            onChange={(e) => onUpdate(item.id, { required: e.target.checked })}
            className="mr-2"
          />
          <label className="text-sm text-gray-700">Required</label>
        </div>

        {["select", "multiselect", "radio", "checkbox"].includes(item.type) && (
          <FieldOptions
            field={{
              ...item,
              id: item.id,
              label: item.title,
              order: 0,
              isArray: false,
              arrayConfig: {
                minItems: 0,
                maxItems: null,
                itemLabel: "Item",
                items: [],
                itemType: "text",
              },
            }}
            updateField={(id, updates) => onUpdate(id, updates)}
          />
        )}
      </div>
    </div>
  );
};

// Preview component for array fields
const ArrayFieldPreview: React.FC<{
  field: Field;
  updateField: (id: string, updates: Partial<Field>) => void;
}> = ({ field, updateField }) => {
  const addItem = () => {
    if (
      field.arrayConfig.maxItems !== null &&
      field.arrayConfig.items.length >= field.arrayConfig.maxItems
    ) {
      return;
    }

    const newItem = {
      id: `item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: `${field.arrayConfig.itemLabel} ${
        field.arrayConfig.items.length + 1
      }`,
    };

    updateField(field.id, {
      arrayConfig: {
        ...field.arrayConfig,
        items: [
          ...field.arrayConfig.items,
          {
            ...newItem,
            type: field.arrayConfig.itemType,
            key: generateUniqueKey(newItem.title),
            required: false,
            placeholder: "",
            helpText: "",
            options: [],
            validation: {},
            defaultValue: null,
          },
        ],
      },
    });
  };

  const removeItem = (itemId: string) => {
    if (field.arrayConfig.items.length <= field.arrayConfig.minItems) {
      return;
    }

    updateField(field.id, {
      arrayConfig: {
        ...field.arrayConfig,
        items: field.arrayConfig.items.filter((item) => item.id !== itemId),
      },
    });
  };

  const updateItemTitle = (itemId: string, newTitle: string) => {
    updateField(field.id, {
      arrayConfig: {
        ...field.arrayConfig,
        items: field.arrayConfig.items.map((item) =>
          item.id === itemId ? { ...item, title: newTitle } : item
        ),
      },
    });
  };

  return (
    <div className="mt-2 space-y-2 border-l-2 pl-3 border-blue-200">
      <p className="text-xs text-gray-500">
        Preview (array of {field.arrayConfig.itemType})
      </p>

      {field.arrayConfig.items.map((item) => (
        <div key={item.id} className="space-y-2">
          <div className="flex items-center gap-2">
            <Input
              type="text"
              value={item.title}
              onChange={(e) => updateItemTitle(item.id, e.target.value)}
              className="flex-1 text-sm"
              placeholder={`${field.arrayConfig.itemLabel} Title`}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => removeItem(item.id)}
              className="text-red-500 hover:text-red-700 p-1"
              disabled={
                field.arrayConfig.items.length <= field.arrayConfig.minItems
              }
            >
              <Minus size={14} />
            </Button>
          </div>
          <div className="pl-4">
            <Input
              type={field.arrayConfig.itemType === "number" ? "number" : "text"}
              disabled
              className="flex-1 text-sm bg-gray-50"
              placeholder="Field value"
            />
          </div>
        </div>
      ))}

      {(field.arrayConfig.maxItems === null ||
        field.arrayConfig.items.length < field.arrayConfig.maxItems) && (
        <Button
          variant="ghost"
          onClick={addItem}
          className="flex items-center text-sm text-blue-500 hover:text-blue-700"
        >
          <Plus size={14} className="mr-1" />
          Add {field.arrayConfig.itemLabel}
        </Button>
      )}
    </div>
  );
};

// Separate component for field configuration to prevent focus loss
const FieldConfigPanel: React.FC<{
  activeField: Field;
  countries?: any[];
  setSelectedCountry?: any;
  updateField: (id: string, updates: Partial<Field>) => void;
  fields: Field[];
}> = ({ activeField, countries, setSelectedCountry, updateField, fields }) => {
  const needsOptions = ["select", "multiselect", "radio", "checkbox"].includes(
    activeField.type
  );
  const isArrayField = activeField.type === "array";

  return (
    <div className="space-y-4">
      {countries && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Country
          </label>
          <Select onValueChange={(value) => setSelectedCountry(value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select a country" />
            </SelectTrigger>
            <SelectContent>
              {countries?.map((country) => (
                <SelectItem key={country.id} value={country.id}>
                  {country.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Label
        </label>
        <Input
          key={`label-${activeField.id}`}
          type="text"
          value={activeField.label}
          onChange={(e) =>
            updateField(activeField.id, { label: e.target.value })
          }
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Field Key
        </label>
        <div className="px-3 py-2 bg-gray-50 border rounded-md text-sm text-gray-600 font-mono">
          {activeField.key ||
            generateUniqueKey(
              activeField.label,
              fields.filter((f) => f.id !== activeField.id).map((f) => f.key)
            )}
        </div>
        <p className="text-xs text-gray-500 mt-1">Auto-generated from label</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Field Type
        </label>
        <Select
          key={`type-${activeField.id}`}
          value={activeField.type}
          onValueChange={(value) => {
            const newType = value as FieldType;
            updateField(activeField.id, {
              type: newType,
              options: ["select", "multiselect", "radio", "checkbox"].includes(
                newType
              )
                ? activeField.options?.length
                  ? activeField.options
                  : []
                : [],
            });
          }}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {FIELD_TYPES.map((type) => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {!isArrayField && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Placeholder
          </label>
          <Input
            key={`placeholder-${activeField.id}`}
            type="text"
            value={activeField.placeholder || ""}
            onChange={(e) =>
              updateField(activeField.id, { placeholder: e.target.value })
            }
            placeholder="Enter placeholder text"
            autoComplete="off"
          />
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Help Text
        </label>
        <Input
          key={`helptext-${activeField.id}`}
          type="text"
          value={activeField.helpText || ""}
          onChange={(e) =>
            updateField(activeField.id, { helpText: e.target.value })
          }
          placeholder="Optional hint text"
          autoComplete="off"
        />
      </div>

      <div className="flex items-center">
        <input
          key={`required-${activeField.id}`}
          type="checkbox"
          id={`required-field-${activeField.id}`}
          checked={activeField.required}
          onChange={(e) =>
            updateField(activeField.id, { required: e.target.checked })
          }
          className="mr-2"
        />
        <label
          htmlFor={`required-field-${activeField.id}`}
          className="text-sm font-medium text-gray-700"
        >
          Required Field
        </label>
      </div>

      {needsOptions && (
        <FieldOptions field={activeField} updateField={updateField} />
      )}

      {isArrayField && (
        <ArrayFieldConfig field={activeField} updateField={updateField} />
      )}
    </div>
  );
};

const FormBuilder: React.FC<FormBuilderProps> = ({
  saveFormValue,
  initialFields = [],
  countries,
  selectedCountry,
  setSelectedCountry,
}) => {
  const [fields, setFields] = useState<Field[]>(
    initialFields.length ? initialFields : [getEmptyField(0)]
  );
  const [activeFieldId, setActiveFieldId] = useState<string | null>(null);
  const [formTitle, setFormTitle] = useState<string>("Untitled Form");
  const [formDescription, setFormDescription] = useState<string>("");
  const [mounted, setMounted] = useState(false);

  // Handle hydration by waiting for client-side mount
  useEffect(() => {
    setMounted(true);
    if (fields.length && !activeFieldId) {
      setActiveFieldId(fields[0]?.id || null);
    }
  }, [fields.length, activeFieldId]);

  const updateField = useCallback((id: string, updates: Partial<Field>) => {
    setFields((prevFields) =>
      prevFields.map((field) => {
        if (field.id === id) {
          const updatedField = { ...field, ...updates };

          // Auto-generate key when label changes
          if (updates.label !== undefined) {
            const existingKeys = prevFields
              .filter((f) => f.id !== id)
              .map((f) => f.key);
            updatedField.key = generateUniqueKey(updates.label, existingKeys);
          }

          return updatedField;
        }
        return field;
      })
    );
  }, []);

  const addField = () => {
    const newField = getEmptyField(fields.length);
    setFields([...fields, newField]);
    setActiveFieldId(newField.id);
  };

  const removeField = (id: string) => {
    if (fields.length === 1) {
      const newField = getEmptyField(0);
      setFields([newField]);
      setActiveFieldId(newField.id);
      return;
    }

    const updatedFields = fields.filter((f) => f.id !== id);
    const reorderedFields = updatedFields.map((field, idx) => ({
      ...field,
      order: idx,
    }));
    setFields(reorderedFields);

    if (activeFieldId === id) {
      setActiveFieldId(reorderedFields[0]?.id || null);
    }
  };

  const duplicateField = (id: string) => {
    const fieldToDuplicate = fields.find((f) => f.id === id);
    if (!fieldToDuplicate) return;

    const newField: Field = {
      ...fieldToDuplicate,
      id: `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      label: `${fieldToDuplicate.label} (Copy)`,
      order: fields.length,
    };

    setFields([...fields, newField]);
    setActiveFieldId(newField.id);
  };

  const handleLabelChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldId: string
  ) => {
    e.stopPropagation();
    updateField(fieldId, { label: e.target.value });
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(fields);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updatedFields = items.map((field, index) => ({
      ...field,
      order: index,
    }));

    setFields(updatedFields);
  };

  const handleSave = () => {
    const fieldsToSave = fields.map(({ id, ...rest }) => ({
      ...rest,
      key: generateUniqueKey(rest.label),
    }));

    saveFormValue({
      title: formTitle,
      description: formDescription,
      fields: fieldsToSave,
      // countryId: selectedCountry,
    });
  };

  // Get active field
  const activeField = fields.find((f) => f.id === activeFieldId);

  // If not yet mounted, show a minimal placeholder to prevent hydration issues
  if (!mounted) {
    return <LoadingSpinner text="" />;
  }

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <SidebarInset className="flex-1">
          <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 border-b bg-background px-4 lg:hidden">
            <div className="flex items-center gap-2 flex-1">
              <h1 className="text-lg font-semibold">Form Builder</h1>
            </div>
            <SidebarTrigger className="-mr-1" />
          </header>

          <main className="flex-1 overflow-auto">
            <div className="relative min-h-full">
              <div className="w-full lg:w-[50%] mx-auto p-4 sm:p-6 lg:p-8">
                {countries?.length == 0 && (
                  <div className="my-3">
                    {/* <ErrorMessage message="No countries available. Please Contact Manager to add countries." /> */}
                  </div>
                )}

                <DragDropContextClient onDragEnd={handleDragEnd}>
                  <DroppableClient droppableId="fields">
                    {(provided) => (
                      <div
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        className="space-y-3"
                      >
                        {fields.map((field, index) => (
                          <DraggableClient
                            key={field.id}
                            draggableId={field.id}
                            index={index}
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                className={`border bg-white   p-4 cursor-pointer transition-all ${
                                  activeFieldId === field.id
                                    ? "ring-2 ring-primary shadow-md"
                                    : "hover:shadow-sm"
                                }`}
                                onClick={() => setActiveFieldId(field.id)}
                              >
                                <div className="flex items-center justify-between gap-2">
                                  <div className="flex items-center flex-1 min-w-0">
                                    <div
                                      {...provided.dragHandleProps}
                                      className="mr-2 cursor-move text-gray-400 hover:text-gray-600 flex-shrink-0"
                                    >
                                      <Move size={16} />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <Input
                                        type="text"
                                        value={field.label}
                                        onChange={(e) =>
                                          handleLabelChange(e, field.id)
                                        }
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setActiveFieldId(field.id);
                                        }}
                                        className="font-medium w-full bg-transparent border-none outline-none p-0 focus:ring-0 text-sm sm:text-base"
                                      />
                                      <p className="text-xs sm:text-sm text-gray-500 truncate">
                                        {
                                          FIELD_TYPES.find(
                                            (t) => t.value === field.type
                                          )?.label
                                        }
                                        {field.required ? " *" : ""}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex space-x-1 flex-shrink-0">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        duplicateField(field.id);
                                      }}
                                      className="p-1 text-gray-400 hover:text-gray-600"
                                      title="Duplicate"
                                    >
                                      <Copy size={16} />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        removeField(field.id);
                                      }}
                                      className="p-1 text-gray-400 hover:text-red-600"
                                      title="Remove"
                                    >
                                      <Trash2 size={16} />
                                    </Button>
                                  </div>
                                </div>

                                {field.type === "array" && (
                                  <ArrayFieldPreview
                                    field={field}
                                    updateField={updateField}
                                  />
                                )}
                              </div>
                            )}
                          </DraggableClient>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </DroppableClient>
                </DragDropContextClient>

                <Button
                  onClick={addField}
                  variant="outline"
                  className="mt-4 flex items-center justify-center w-full py-8 border-2 border-dashed border-gray-300 rounded-md text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors bg-transparent"
                >
                  <PlusCircle size={18} className="mr-2" />
                  Add Field
                </Button>
              </div>
            </div>
          </main>
        </SidebarInset>

        <Sidebar side="right" className="border-l lg:w-[20%] w-80">
          <SidebarContent className="p-6 lg:p-8 overflow-y-auto">
            <div className="mb-6">
              <input
                type="text"
                value={formTitle}
                required
                onChange={(e) => setFormTitle(e.target.value)}
                className="w-full text-2xl font-bold mb-2 px-2 py-1 border-b border-transparent hover:border-gray-300 focus:border-blue-500 focus:outline-none bg-transparent"
                placeholder="Form Title"
              />
              <textarea
                required
                value={formDescription}
                onChange={(e) => setFormDescription(e.target.value)}
                className="w-full text-gray-600 px-2 py-1 border-b border-transparent hover:border-gray-300 focus:border-blue-500 focus:outline-none resize-none bg-transparent"
                placeholder="Form Description (optional)"
                rows={2}
              />
            </div>

            {activeField && (
              <div>
                <h3 className="text-lg font-medium mb-4">Field Properties</h3>
                <FieldConfigPanel
                  activeField={activeField}
                  countries={countries}
                  setSelectedCountry={setSelectedCountry}
                  updateField={updateField}
                  fields={fields}
                />
              </div>
            )}

            <div className="mt-6 flex justify-end">
              <Button
                onClick={handleSave}
                className="shadow-lg shadow-primary/20 rounded"
                disabled={
                  fields.length === 0 ||
                  !selectedCountry ||
                  countries?.length === 0
                }
              >
                Save Form
              </Button>
            </div>
          </SidebarContent>
        </Sidebar>
      </div>
    </SidebarProvider>
  );
};

export default FormBuilder;
