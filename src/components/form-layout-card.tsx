import React from "react";
import { FormHeader } from "@/components/form-header";
interface formLayoutCardProps {
  title: string;
  description: string;
  children: React.ReactNode;
}
export function FormLayoutCard({
  title,
  description,
  children,
}: formLayoutCardProps) {
  return (
    <div className="flex flex-col gap-y-3">
      <FormHeader title={title} description={description} />
      {children}
    </div>
  );
}
