"use client";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useState, useMemo } from "react";
import { LoadingSpinner } from "./loading-spinner";
import EmptyList from "./empty-list";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Calendar,
  MapPin,
  FileText,
  MoreHorizontal,
  Eye,
  Edit,
  Filter,
} from "lucide-react";

interface FormField {
  id: string;
  label: string;
  type: string;
  key: string;
  required: boolean;
  order: number;
}

interface Country {
  id: string;
  name: string;
  code: string;
  flag: string;
}

interface Form {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  isPublished: boolean;
  fields: FormField[];
  country: Country;
}

export default function FormList() {
  const router = useRouter();
  const [filter, setFilter] = useState<"all" | "published" | "draft">("all");

  const { data, isLoading, isError } = useQuery({
    queryKey: ["forms"],
    queryFn: async () => {
      const { data } = await axios.get("/api/v1/forms");
      return data;
    },
  });

  const filteredData = useMemo(() => {
    if (!data) return [];
    if (filter === "all") return data;
    if (filter === "published")
      return data.filter((form: Form) => form.isPublished);
    if (filter === "draft")
      return data.filter((form: Form) => !form.isPublished);
    return data;
  }, [data, filter]);

  if (isLoading) return <LoadingSpinner text="Loading..." />;
  if (isError) return <div>Error</div>;
  if (!data.length)
    return (
      <EmptyList
        text="No forms found"
        onClick={() => router.push("/forms/create")}
        buttonText="Create Form"
      />
    );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-transparent rounded-none"
              >
                <Filter className="h-4 w-4" />
                {filter === "all"
                  ? "All Forms"
                  : filter === "published"
                  ? "Published"
                  : "Draft"}
                {filter !== "all" && (
                  <Badge
                    variant="secondary"
                    className="ml-1 h-5 px-1.5 text-xs"
                  >
                    {filteredData.length}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="rounded-none">
              <DropdownMenuItem onClick={() => setFilter("all")}>
                All Forms ({data.length})
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter("published")}>
                Published (
                {data.filter((form: Form) => form.isPublished).length})
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilter("draft")}>
                Draft ({data.filter((form: Form) => !form.isPublished).length})
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={() => router.push("/forms/create")}>
            Create New Form
          </Button>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredData.length} of {data.length} forms
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-4">
        {filteredData.map((form: Form) => (
          <Card
            key={form.id}
            className="hover:shadow-md rounded-none transition-shadow"
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1 min-w-0 flex-1">
                  <CardTitle className="text-lg truncate">
                    {form.title}
                  </CardTitle>
                  <CardDescription className="line-clamp-1 text-sm">
                    {form.description}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2 ml-2">
                  <Badge
                    variant={form.isPublished ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {form.isPublished ? "Published" : "Draft"}
                  </Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="rounded-none">
                      <DropdownMenuItem
                        onClick={() => router.push(`/forms/${form.id}/view`)}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        View
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => router.push(`/apply/${form.id}`)}
                      >
                        <FileText className="mr-2 h-4 w-4" />
                        Apply
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => router.push(`/forms/${form.id}/edit`)}
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-2 pt-0">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <MapPin className="h-3 w-3" />
                <img
                  src={form.country.flag || "/placeholder.svg"}
                  alt={form.country.name}
                  className="h-3 w-4 object-cover rounded-sm"
                />
                <span className="truncate">{form.country.name}</span>
              </div>

              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <FileText className="h-3 w-3" />
                <span>{form.fields.length} fields</span>
              </div>

              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>
                  Created {new Date(form.createdAt).toLocaleDateString()}
                </span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
