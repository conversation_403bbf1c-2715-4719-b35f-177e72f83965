"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "../ui/button";
import { useSession } from "next-auth/react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { LoadingSpinner } from "../loading-spinner";

export default function Header() {
  const { data: session, status } = useSession();
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const user = session?.user;

  return (
    <div
      className={cn(
        " fixed w-full top-0 z-50 transition-all duration-200",
        isScrolled
          ? "bg-background/80 backdrop-blur-md border-b shadow-sm"
          : "bg-transparent"
      )}
    >
      <div className="container mx-auto p-4 flex justify-between items-center">
        <div className="text-xl font-extrabold">Eureka</div>
        <div className="flex gap-x-8">
          <Link href={"#"}>Home</Link>
          <Link href={"/"}>About</Link>
          <Link href={"/"}>FAQ</Link>
          <Link href={"/"}>Contact</Link>
        </div>

        {status == "loading" ? (
          <div className="h-8 w-8 rounded-full bg-gray-300">
            <LoadingSpinner text="Loading..." />
          </div>
        ) : (
          <div className="flex items-center gap-x-5">
            {session?.user ? (
              <div className="flex items-center gap-x-2">
                <Avatar>
                  <AvatarImage src={session.user.image || ""} />
                  <AvatarFallback>
                    {session.user.name?.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  {user?.role === "AGENT" ||
                  user?.role === "MANAGER" ||
                  user?.role === "GENERAL_MANAGER" ? (
                    <Link href="/main">Dashboard</Link>
                  ) : (
                    <Link href="/client">Client Portal</Link>
                  )}
                </div>
              </div>
            ) : (
              <>
                <Button variant={"outline"}>Client</Button>
                <Button>Agent</Button>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
