"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DialogClose } from "@radix-ui/react-dialog";

interface BaseModalProps {
  title: string;
  description: string;

  children: React.ReactNode;
  onClose?: () => void;

  isOpen: boolean;
}
export function BaseModal({
  title,
  description,
  children,
  onClose,
  isOpen,
}: BaseModalProps) {
  return (
    <Dialog modal onOpenChange={onClose} open={isOpen}>
      <DialogClose></DialogClose>
      <DialogContent className="rounded-none w-full px-10 py-10">
        <DialogHeader>
          <DialogTitle className="text-2xl">{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        {children}
      </DialogContent>
    </Dialog>
  );
}
