"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { BaseModal } from "./base";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";
import { toast } from "sonner";

const createAgencySchema = z.object({
  name: z.string().min(1, "Name is required"),
  location: z.string().min(1, "Location is required"),
  email: z.email("Invalid email").min(1, "Email is required"),
  phone: z.string().min(1, "Phone is required"),
});

type CreateAgencyForm = z.infer<typeof createAgencySchema>;

interface AgencyModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AgencyModal({ isOpen, onClose }: AgencyModalProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateAgencyForm>({
    resolver: zodResolver(createAgencySchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CreateAgencyForm) => {
      const res = await axios.post("/api/v1/agencies", data);
      return res.data;
    },
    onSuccess: () => {
      reset();
      toast.success("Agency created successfully");
      onClose();
    },
    onError: (error) => {
      console.error(error);
      toast.error("Failed to create agency");
    },
  });

  const onSubmit = async (data: CreateAgencyForm) => {
    mutate(data);
  };

  return (
    <BaseModal
      title="Create Agency"
      description="Create a new agency by filling out the form below with the required information."
      isOpen={isOpen}
      onClose={onClose}
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="name" className="text-sm font-medium">
            Agency Name
          </label>
          <Input {...register("name")} />
          {errors.name && (
            <span className="text-destructive text-sm">
              {errors.name.message}
            </span>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="location" className="text-sm font-medium">
            Location
          </label>
          <Input {...register("location")} />
          {errors.location && (
            <span className="text-destructive text-sm">
              {errors.location.message}
            </span>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="email" className="text-sm font-medium">
            Email
          </label>
          <Input {...register("email")} type="email" />
          {errors.email && (
            <span className="text-destructive text-sm">
              {errors.email.message}
            </span>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="phone" className="text-sm font-medium">
            Phone
          </label>
          <Input {...register("phone")} />
          {errors.phone && (
            <span className="text-destructive text-sm">
              {errors.phone.message}
            </span>
          )}
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" type="button" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" disabled={isPending}>
            Create Agency
          </Button>
        </div>
      </form>
    </BaseModal>
  );
}
