"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { BaseModal } from "./base";
import { Button } from "../ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import { FileText, ImageIcon, Upload } from "lucide-react";
import { toast } from "sonner";
import { agentDocuments, AgentsDocument } from "@/lib/schemas";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";

interface DocumentsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function DocumentsModal({ isOpen, onClose }: DocumentsModalProps) {
  const queryClient = useQueryClient();
  const form = useForm<AgentsDocument>({
    resolver: zodResolver(agentDocuments),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: (formdata: FormData) =>
      axios.post("/api/v1/users/documents/", formdata, {
        headers: {
          "Content-Type": "Multipart/formdata",
        },
      }),
    onSuccess() {
      toast.success("Documents uploaded successfully");
      queryClient.invalidateQueries({ queryKey: ["user"] });
      form.reset();

      onClose();
    },
    onError() {
      toast.error("Failed to upload documents");
    },
  });

  const getFileIcon = (file: File | undefined) => {
    if (!file)
      return (
        <div className="flex flex-col items-center justify-center p-4 border-2 border-dashed rounded-lg border-gray-300 w-full">
          <Upload className="h-8 w-8 text-gray-400 mb-2" />
          <span className="text-sm text-gray-500">Click to upload file</span>
        </div>
      );

    if (file.type === "application/pdf") {
      return (
        <div className="flex items-center justify-center p-2 bg-red-50 rounded-lg w-full">
          <FileText className="h-6 w-6 text-red-500 mr-2" />
          <span className="text-sm text-red-700">{file.name}</span>
        </div>
      );
    }
    return (
      <div className="flex items-center justify-center p-2 bg-blue-50 rounded-lg w-full">
        <ImageIcon className="h-6 w-6 text-blue-500 mr-2" />
        <span className="text-sm text-blue-700">{file.name}</span>
      </div>
    );
  };

  const onSubmit = (data: AgentsDocument) => {
    const formdata = new FormData();
    formdata.append("idCardFront", data.idCardFront);
    formdata.append("idCardBack", data.idCardBack);
    formdata.append("locationPlan", data.locationPlan);
    mutate(formdata);
  };

  return (
    <BaseModal
      title="Upload Documents"
      description="Please upload the required documents below"
      isOpen={isOpen}
      onClose={onClose}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-2 gap-3">
            <FormField
              control={form.control}
              name="idCardFront"
              render={({ field: { onChange, value, ...field } }) => (
                <FormItem>
                  <FormLabel className="text-base">ID Card (Front)</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*,.pdf"
                        onChange={(e) => onChange(e.target.files?.[0])}
                        className="opacity-0 absolute inset-0 w-full h-full cursor-pointer"
                        {...field}
                      />
                      {getFileIcon(value as File)}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="idCardBack"
              render={({ field: { onChange, value, ...field } }) => (
                <FormItem>
                  <FormLabel className="text-base">ID Card (Back)</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*,.pdf"
                        onChange={(e) => onChange(e.target.files?.[0])}
                        className="opacity-0 absolute inset-0 w-full h-full cursor-pointer"
                        {...field}
                      />
                      {getFileIcon(value as File)}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="locationPlan"
              render={({ field: { onChange, value, ...field } }) => (
                <FormItem>
                  <FormLabel className="text-base">Location Plan</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*,.pdf"
                        onChange={(e) => onChange(e.target.files?.[0])}
                        className="opacity-0 absolute inset-0 w-full h-full cursor-pointer"
                        {...field}
                      />
                      {getFileIcon(value as File)}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end gap-3 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isPending}>
              Upload Documents
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  );
}
