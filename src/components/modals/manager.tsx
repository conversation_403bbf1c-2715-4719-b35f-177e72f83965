"use client";

import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { BaseModal } from "./base";
import { Button } from "../ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import { mangerSchema, type Manger } from "@/lib/schemas";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

interface ManagerModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ManagerModal({ isOpen, onClose }: ManagerModalProps) {
  const { data: agencies } = useQuery({
    queryKey: ["agencies"],
    queryFn: async () => {
      const res = await axios.get("/api/v1/agencies");
      return res.data;
    },
  });

  const form = useForm<Manger>({
    resolver: zodResolver(mangerSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      password: "",
      agencyId: "",
    },
  });

  const onSubmit = async (data: Manger) => {
    try {
      // Handle form submission here
      console.log(data);
      onClose();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <BaseModal
      title="Add New Manager"
      description="Create a new manager account"
      isOpen={isOpen}
      onClose={onClose}
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter manager name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="Enter manager email"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone</FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder="Enter manager phone"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="Enter manager password"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="agencyId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Agency</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <SelectTrigger className="w-full rounded-none">
                      <SelectValue placeholder="Select an agency" />
                    </SelectTrigger>
                    <SelectContent className="rounded-none">
                      {agencies?.map((agency: any) => (
                        <SelectItem key={agency.id} value={agency.id}>
                          {agency.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-4">
            <Button variant="outline" type="button" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Create Manager</Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  );
}
