"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { BaseModal } from "./base";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { toast } from "sonner";

const createCountrySchema = z.object({
  name: z.string().min(1, "Country name is required"),
});

type CreateCountryForm = z.infer<typeof createCountrySchema>;

interface CountryModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function CountryModal({ isOpen, onClose }: CountryModalProps) {
  const queryClient = useQueryClient();
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CreateCountryForm>({
    resolver: zodResolver(createCountrySchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CreateCountryForm) => {
      const response = await axios.post("/api/v1/countries", data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["countries"] });
      toast.success("Country created successfully");
      reset();
      onClose();
    },
    onError: () => {
      toast.error("Failed to create country");
    },
  });

  const onSubmit = (data: CreateCountryForm) => {
    mutate(data);
  };

  return (
    <BaseModal
      title="Create Country"
      description="Add a new country to the system"
      isOpen={isOpen}
      onClose={onClose}
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="name" className="text-sm font-medium">
            Country Name
          </label>
          <Input {...register("name")} placeholder="Enter country name" />
          {errors.name && (
            <span className="text-destructive text-sm">
              {errors.name.message}
            </span>
          )}
        </div>

        <Button type="submit" className="w-full" disabled={isPending}>
          {isPending ? "Creating..." : "Create Country"}
        </Button>
      </form>
    </BaseModal>
  );
}
