/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { ColumnDef } from "@tanstack/react-table";
import { Agent } from "@/types/agents";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import React from "react";
import { DataTable } from "./base";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";
import { Info, MoreHorizontal, Power, Trash2 } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { LoadingSpinner } from "../loading-spinner";
import EmptyList from "../empty-list";
interface FormatedAgent {
  id: string;
  avatar: string;
  agencyName: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  activated: boolean;
  referralCode: string;
  createdAt: string;
}
export default function AgentsTable({ onAdd }: { onAdd: () => void }) {
  const queryClient = useQueryClient();
  const { data, isLoading } = useQuery<Agent[]>({
    queryKey: ["agents"],
    queryFn: async () => (await axios.get("/api/v1/agents")).data,
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: string) =>
      await axios.delete(`/api/v1/agents/${id}`),
    onSuccess: () => {
      toast.success("Agent deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
    onError: (error) => {
      toast.error("Failed to delete agent");
      console.error(error);
    },
  });

  const activateMutation = useMutation({
    mutationFn: async (id: string) =>
      await axios.post(`/api/v1/agents/${id}/activate`),
    onSuccess: () => {
      toast.success("Agent activated successfully");
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
    onError: (error) => {
      toast.error("Failed to activate agent");
      console.error(error);
    },
  });

  const disactivateMutation = useMutation({
    mutationFn: async (id: string) =>
      await axios.post(`/api/v1/agents/${id}/disactivate`),
    onSuccess: () => {
      toast.success("Agent disactivated successfully");
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
    onError: (error) => {
      toast.error("Failed to disactivate agent");
      console.error(error);
    },
  });

  if (isLoading) return <LoadingSpinner text="No Agents yet" />;
  if (!data || data?.length == 0)
    return (
      <EmptyList onClick={onAdd} text="No Agents" buttonText="Add Agent" />
    );

  const formatedData = data.map((agent) => ({
    id: agent.id,
    avatar: agent.user.profile.avatar,
    agencyName: agent.agency.name,
    name: agent.user.name,
    email: agent.user.email,
    phone: agent.user.phone,
    role: agent.user.role,
    activated: agent.user.activated,
    referralCode: agent.user.referralCode,
    createdAt: agent.user.createdAt,
  }));
  const columns: ColumnDef<FormatedAgent, any>[] = [
    {
      accessorKey: "avatar",
      header: "Avatar",
      cell: ({ row }) => (
        <div className="w-12 h-12 rounded overflow-hidden">
          <img
            src={row.original.avatar}
            alt={row.original.name}
            className="w-full h-full object-cover"
          />
        </div>
      ),
    },
    {
      accessorKey: "agencyName",
      header: "Agency",
    },
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "phone",
      header: "Phone",
    },
    {
      accessorKey: "role",
      header: "Role",
    },
    {
      accessorKey: "activated",
      header: "Activated",
      cell: ({ getValue }) => (getValue() ? "Yes" : "No"),
    },
    {
      accessorKey: "referralCode",
      header: "Referral Code",
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ getValue }) => {
        const date = getValue() as string;
        return new Date(date).toLocaleDateString();
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="rounded-none">
              <DropdownMenuItem
                onClick={() => {
                  deleteMutation.mutate(row.original.id);
                }}
                disabled={deleteMutation.isPending}
              >
                <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                Delete
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  row.original.activated
                    ? disactivateMutation.mutate(row.original.id)
                    : activateMutation.mutate(row.original.id);
                }}
                disabled={
                  activateMutation.isPending || disactivateMutation.isPending
                }
              >
                <Power className="mr-2 h-4 w-4" />
                {row.original.activated ? "Deactivate" : "Activate"}
              </DropdownMenuItem>
              <DropdownMenuItem
                asChild
                disabled={
                  deleteMutation.isPending || activateMutation.isPending
                }
              >
                <Link href={`/main/agents/${row.original.id}`}>
                  <Info className="mr-2 h-4 w-4" />
                  Details
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return (
    <DataTable
      data={formatedData}
      columns={columns}
      filterColumn="name"
      onExport={() => console.log("Export")}
      onAdd={onAdd}
    />
  );
}
