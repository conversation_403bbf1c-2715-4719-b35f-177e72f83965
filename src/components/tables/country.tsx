/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "./base";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Trash2 } from "lucide-react";
import { toast } from "sonner"; // if you have one
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Country {
  id: string;
  name: string;
  code: string;
  capital: string;
  currency: string;
  applications: any[];
  timezone: string;
  population: number;
  flag: string;
}

export default function CountryTable() {
  const queryClient = useQueryClient();
  const { mutate, isPending } = useMutation({
    mutationFn: async (id: string) =>
      await axios.delete(`/api/v1/countries/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["countries"] });
      toast.success("Deleted successfully.");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
  const { data } = useQuery({
    queryKey: ["countries"],
    queryFn: async () => (await axios.get("/api/v1/countries")).data,
  });
  const columns: ColumnDef<Country, any>[] = [
    {
      accessorKey: "flag",
      header: "Flag",
      cell: ({ row }) => (
        <div className="w-12 h-12 rounded overflow-hidden">
          <img
            src={row.original.flag}
            alt={row.original.name}
            className="w-full"
          />
        </div>
      ),
    },
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "code",
      header: "Code",
    },
    {
      accessorKey: "capital",
      header: "Capital",
    },
    {
      accessorKey: "currency",
      header: "Currency",
    },
    {
      accessorKey: "timezone",
      header: "Timezone",
    },
    {
      accessorKey: "population",
      header: "Population",
    },
    {
      accessorKey: "applications",
      header: "Applications",
      cell: ({ row }) => row.original.applications?.length ?? 0,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  mutate(row.original.id);
                }}
              >
                <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                Delete
              </DropdownMenuItem>
              <DropdownMenuItem></DropdownMenuItem>
              <DropdownMenuItem></DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  if (!data) return <div className="p-4">Loading countries...</div>;

  return <DataTable data={data} columns={columns} filterColumn="name" />;
}
