/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @next/next/no-img-element */
"use client";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { LoadingSpinner } from "../loading-spinner";
import { Formated<PERSON>anager, Manager } from "@/types/manager";
import { DataTable } from "./base";
import { ColumnDef } from "@tanstack/react-table";
import { formatDate } from "date-fns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";
import { Edit, Info, MoreHorizontal, Power, Trash2 } from "lucide-react";
import Link from "next/link";

interface ManagerTableProps {
  onAdd: () => void;
  onExport: () => void;
}

export default function ManagerTable({ onAdd, onExport }: ManagerTableProps) {
  const { data, isLoading, error } = useQuery<Manager[]>({
    queryKey: ["managers"],
    queryFn: async () => (await axios.get("/api/v1/managers")).data,
  });

  const formattedData: FormatedManager[] | undefined = data?.map((manager) => ({
    id: manager.id,
    avatar: manager.user.profile.avatar,
    name: manager.user.name,
    email: manager.user.email,
    phone: manager.user.phone,
    role: manager.user.role,
    activated: manager.user.activated,
    agency: manager.agency.name,
    createdAt: manager.createdAt,
  }));

  const columns: ColumnDef<FormatedManager, any>[] = [
    {
      accessorKey: "avatar",
      header: "Avatar",
      cell: ({ row }) => (
        <div className="w-12 h-12 rounded overflow-hidden">
          <img
            src={row.original.avatar}
            alt={row.original.name}
            className="w-full h-full object-cover"
          />
        </div>
      ),
    },
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "phone",
      header: "Phone",
    },
    {
      accessorKey: "role",
      header: "Role",
    },
    {
      accessorKey: "activated",
      header: "Activated",
      cell: ({ getValue }) => (getValue() ? "Yes" : "No"),
    },

    {
      accessorKey: "agency",
      header: "Agency",
    },
    {
      accessorKey: "createdAt",
      header: "Created At",
      cell: ({ getValue }) => {
        const date = getValue() as string;
        return formatDate(new Date(date), "PP");
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="rounded-none w-[180px]">
              <DropdownMenuItem>
                <Trash2 className="mr-2 h-4 w-4 text-red-600" />
                Delete
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Power className="mr-2 h-4 w-4" />
                {row.original.activated ? "Deactivate" : "Activate"}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Power className="mr-2 h-4 w-4" />
                Demote
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/main/managers/${row.original.id}`}>
                  <Info className="mr-2 h-4 w-4" />
                  Details
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  if (isLoading) return <LoadingSpinner text="Loading..." />;
  if (error) return <div>{error.message}</div>;
  if (!formattedData) return <div>No data</div>;
  return (
    <div>
      <DataTable
        data={formattedData}
        columns={columns}
        filterColumn="name"
        onAdd={onAdd}
        onExport={onExport}
      />
    </div>
  );
}
