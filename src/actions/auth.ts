/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import { loginSchema } from "@/lib/schemas/login";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";

export async function login(credentials: any) {
  const validatedFields = loginSchema.safeParse(credentials);
  if (!validatedFields.success) {
    return null;
  }

  try {
    const user = await prisma.user.findUnique({
      where: {
        email: validatedFields.data.email,
      },
    });

    if (!user) {
      return null;
    }

    const passwordMatch = await bcrypt.compare(
      validatedFields.data.password,
      user.password
    );

    if (!passwordMatch) {
      return null;
    }

    return user;
  } catch (error) {
    return null;
  }
}

// export async function clientLogin(data: Login) {
//   const validatedFields = loginSchema.safeParse(data);
//   if (!validatedFields.success) {
//     return { error: "Invalid credentials" };
//   }

//   const user = await prisma.user.findUnique({
//     where: {
//       email: validatedFields.data.email,
//     },
//   });
//   if (!user) {
//     return { error: "Invalid credentials" };
//   }
//   try {
//     await signIn("credentials", {
//       email: validatedFields.data.email,
//       password: validatedFields.data.password,
//       redirectTo: "/main",
//     });
//   } catch (error) {
//     if (error instanceof AuthError) {
//       switch (error.type) {
//         case "CredentialsSignin":
//           return { error: "Invalid Credentials" };
//         default:
//           return { error: "Something went wrong" };
//       }
//     }
//     throw error;
//   }
// }
