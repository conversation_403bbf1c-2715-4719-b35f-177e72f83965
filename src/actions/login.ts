"use server";
import { signIn } from "@/auth";
import { Login, loginSchema } from "@/lib/schemas/login";
import { AuthError } from "next-auth";

export async function login(data: Login, redirectUrl?: string) {
  const validatedFields = loginSchema.safeParse(data);
  if (!validatedFields.success) {
    return { error: "Invalid credentials" };
  }
  try {
    await signIn("credentials", {
      email: data.email,
      password: data.password,
      redirectTo: redirectUrl,
    });
    return { success: "Logged in successfully" };
  } catch (error) {
    if (error instanceof AuthError) {
      switch (error.type) {
        case "CredentialsSignin":
          return { error: "Invalid Credentials" };
        default:
          return { error: "Something went wrong" };
      }
    }
    throw error;
  }
}
