"use server";

import { prisma } from "@/lib/prisma";

export async function createNotification({
  message,
  type,
  userId,
  applicantId,
}: {
  message: string;
  type: string;
  userId?: string;
  applicantId?: string;
}) {
  try {
    const notification = await prisma.notification.create({
      data: {
        message,
        type,
        userId,
        applicantId,
      },
    });
    return notification;
  } catch (error) {
    throw error;
  }
}

export async function getUserNotifications(userId: string) {
  try {
    const notifications = await prisma.notification.findMany({
      where: {
        userId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
    return notifications;
  } catch (error) {
    throw error;
  }
}

export async function getApplicantNotifications(applicantId: string) {
  try {
    const notifications = await prisma.notification.findMany({
      where: {
        applicantId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });
    return notifications;
  } catch (error) {
    throw error;
  }
}
