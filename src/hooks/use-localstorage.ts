"use client";

import { useEffect, useState } from "react";

export function useLocalStorage<T>(key: string, initialValue: T) {
  const [value, setValue] = useState<T>(initialValue);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    try {
      const item = localStorage.getItem(key);
      if (item) {
        setValue(JSON.parse(item));
      }
    } catch {
    } finally {
      setIsLoaded(true);
    }
  }, [key]);

  const updateValue = (newValue: T | ((prev: T) => T)) => {
    try {
      const valueToStore =
        typeof newValue === "function"
          ? (newValue as (prev: T) => T)(value)
          : newValue;
      setValue(valueToStore);
      localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch {}
  };

  return [value, updateValue, isLoaded] as const;
}
