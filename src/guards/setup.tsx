"use client";

import type React from "react";
import { checkCompanySetup } from "@/actions/company";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { useLocalStorage } from "@/hooks/use-localstorage";
import { LoadingSpinner } from "@/components/loading-spinner";

interface SetupGuardProps {
  children: React.ReactNode;
  onboardingPath?: string;
  redirectPath?: string;
}

export default function SetupGuard({
  children,
  onboardingPath = "/onboarding",
  redirectPath = "/",
}: SetupGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [cachedSetup, setCachedSetup, isCacheLoaded] = useLocalStorage<
    boolean | null
  >("company-setup-status", null);

  const { data: isSetup, isLoading } = useQuery({
    queryKey: ["company-setup"],
    queryFn: checkCompanySetup,
    enabled: cachedSetup === null,
  });

  const setUpPaths = [
    "/onboarding",
    "/onboarding/contact",
    "/onboarding/headoffice",
    "/onboarding/password",
    "/onboarding/manager",
  ];
  useEffect(() => {
    if (isSetup !== undefined) setCachedSetup(isSetup);
    const setupStatus = isSetup ?? cachedSetup;
    if (setupStatus === false && pathname !== onboardingPath) {
      if (setUpPaths.includes(pathname)) return;
      router.push(onboardingPath);
    } else if (setupStatus === true && pathname === onboardingPath) {
      router.push(redirectPath);
    }
  }, [
    isSetup,
    cachedSetup,
    setCachedSetup,
    router,
    onboardingPath,
    redirectPath,
    pathname,
  ]);

  const setupStatus = isSetup ?? cachedSetup;

  if (!isCacheLoaded || (isLoading && cachedSetup === null))
    return <LoadingSpinner text="Checking setup status..." />;

  if (
    setupStatus === false &&
    pathname !== onboardingPath &&
    !setUpPaths.includes(pathname)
  )
    return <LoadingSpinner text="Redirecting to setup..." />;

  if (
    setupStatus === true &&
    pathname === onboardingPath &&
    !setUpPaths.includes(pathname)
  )
    return <LoadingSpinner text="Redirecting..." />;

  if (
    setupStatus === true ||
    pathname === onboardingPath ||
    setUpPaths.includes(pathname)
  ) {
    return <>{children}</>;
  }

  return null;
}
