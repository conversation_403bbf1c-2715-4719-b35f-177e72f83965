"use client";

import type React from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { useEffect } from "react";
import { LoadingSpinner } from "@/components/loading-spinner";

interface AuthGuardProps {
  children: React.ReactNode;
  authPaths?: string[];
  redirectPath?: string;
  requireAuth?: boolean;
  fallbackPath?: string;
}

export default function AuthGuard({
  children,
  authPaths = ["/login", "/forgot-password", "/reset-password"],
  redirectPath = "/",
  requireAuth = true,
  fallbackPath = "/login",
}: AuthGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { data: session, status } = useSession();

  const isAuthPath = authPaths.some(
    (path) => pathname === path || pathname.startsWith(`${path}/`)
  );

  useEffect(() => {
    if (status === "loading") return;

    const isAuthenticated = !!session;

    if (requireAuth) {
      if (!isAuthenticated && !isAuthPath) {
        const returnUrl =
          pathname !== "/" ? `?returnUrl=${encodeURIComponent(pathname)}` : "";
        router.push(`${fallbackPath}${returnUrl}`);
      }
    } else {
      if (isAuthenticated && isAuthPath) {
        const urlParams = new URLSearchParams(window.location.search);
        const returnUrl = urlParams.get("returnUrl");
        if (
          returnUrl &&
          returnUrl.startsWith("/") &&
          !authPaths.includes(returnUrl)
        ) {
          router.push(decodeURIComponent(returnUrl));
        } else {
          router.push(redirectPath);
        }
      }
    }
  }, [
    session,
    status,
    router,
    authPaths,
    redirectPath,
    pathname,
    requireAuth,
    fallbackPath,
    isAuthPath,
  ]);

  if (status === "loading") {
    return <LoadingSpinner text="Checking authentication..." />;
  }

  const isAuthenticated = !!session;

  if (requireAuth) {
    if (!isAuthenticated && !isAuthPath) {
      return <LoadingSpinner text="Redirecting to login..." />;
    }

    if (isAuthenticated || isAuthPath) {
      return <>{children}</>;
    }
  } else {
    if (isAuthenticated && isAuthPath) {
      return <LoadingSpinner text="Redirecting..." />;
    }

    if (!isAuthenticated || !isAuthPath) {
      return <>{children}</>;
    }
  }

  return null;
}
