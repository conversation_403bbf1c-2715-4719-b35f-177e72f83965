"use client";
import ManagerModal from "@/components/modals/manager";
import ManagerTable from "@/components/tables/manager";
// import { ManagerModal } from "@/components/modals/manager";
// import { useGetManagers } from "@/hooks/use-managers";
import { useState } from "react";

export default function Managers() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Managers</h1>
      <ManagerTable onAdd={() => setIsOpen(true)} onExport={() => {}} />
      <ManagerModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </div>
  );
}
