"use client";
import ManagerModal from "@/components/modals/manager";
// import { ManagerModal } from "@/components/modals/manager";
// import { useGetManagers } from "@/hooks/use-managers";
import { useState } from "react";

export default function Managers() {
  const [isOpen, setIsOpen] = useState(false);
  // const { data, error, isLoading } = useGetManagers();

  // if (error) return <div>{error.message}</div>;
  // if (isLoading) return <div>Loading...</div>;
  // if (!data) return <div>No data</div>;

  return (
    <div>
      <button onClick={() => setIsOpen(true)}>Open Modal</button>
      <ManagerModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </div>
  );
}
