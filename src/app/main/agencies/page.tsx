/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { AgencyCard } from "@/components/agency-card";
import { LoadingSpinner } from "@/components/loading-spinner";
import { AgencyModal } from "@/components/modals/agency";
import { useGetAgencies } from "@/hooks/use-agencies";
import { Agency } from "@/types/agency";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

export default function Agencies() {
  const [isOpen, setIsOpen] = useState(false);
  const { data, error, isLoading } = useGetAgencies();
  const agencies = data ?? ([] as Agency[]);

  if (error) return <div>{error.message}</div>;
  if (isLoading) return <LoadingSpinner text="Loading..." />;

  const handleViewAgency = (agency: Agency) => {
    console.log("Viewing agency", agency);
  };
  const handleEditAgency = (agency: Agency) => {
    console.log("Editing agency", agency);
  };
  const onAdd = () => {
    setIsOpen(true);
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Agencies</h1>
      <div className="flex justify-end mb-4">
        <Button size="sm" onClick={onAdd}>
          <Plus />
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {agencies.map((agency) => (
          <AgencyCard
            key={agency.id}
            agency={agency as any}
            onView={handleViewAgency}
            onEdit={handleEditAgency}
          />
        ))}
      </div>

      <AgencyModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </div>
  );
}
