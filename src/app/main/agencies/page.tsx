"use client";
import { AgencyModal } from "@/components/modals/agency";
import { useGetAgencies } from "@/hooks/use-agencies";
import { useState } from "react";

export default function Agencies() {
  const [isOpen, setIsOpen] = useState(false);
  const { data, error, isLoading } = useGetAgencies();

  if (error) return <div>{error.message}</div>;
  if (isLoading) return <div>Loading...</div>;
  if (!data) return <div>No data</div>;

  return (
    <div>
      <button onClick={() => setIsOpen(true)}>Open Modal</button>
      <AgencyModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </div>
  );
}
