/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { LoadingSpinner } from "@/components/loading-spinner";
import { Input } from "@/components/ui/input";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { DocumentsModal } from "@/components/modals/documents";
import { FileText } from "lucide-react";
import { toast } from "sonner";

export default function Profile() {
  const [uploadFile, setuploadFile] = useState(false);
  const queryClient = useQueryClient();
  const form = useForm<any>({
    defaultValues: {
      name: "",
      email: "",
      dateOfBirth: "",
      firstName: "",
      lastName: "",
      documents: [],
    },
  });
  const { data, isLoading } = useQuery({
    queryKey: ["user"],
    queryFn: async () => (await axios.get("/api/v1/users")).data,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: () => axios.post("/api/v1/users/referal-code"),
    onSuccess() {
      toast.success("Referral code generated successfully");
      queryClient.invalidateQueries({ queryKey: ["user"] });
      form.reset();
    },
    onError() {
      toast.error("Failed to generate referral code");
    },
  });

  const { register, reset } = useForm({
    defaultValues: {
      name: "",
      email: "",
      dateOfBirth: "",
      firstName: "",
      lastName: "",
      documents: [],
    },
  });

  useEffect(() => {
    if (data) {
      reset({
        name: data.name,
        email: data.email,
        dateOfBirth: data.profile.dateOfBirth,
        firstName: data.profile.firstName,
        lastName: data.profile.lastName,
        documents: data.profile.documents,
      });
    }
  }, [data, reset]);

  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  if (isLoading) return <LoadingSpinner text="Loading..." />;

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <form className="flex flex-col gap-6">
        <div className="flex flex-col items-center gap-4">
          <div className="relative w-32 h-32 group">
            <div className="w-32 h-32 rounded-xl overflow-hidden">
              <img
                src={previewImage || data?.profile?.avatar}
                alt="Profile"
                className="w-full h-full object-cover"
              />
            </div>
            <div
              className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer rounded-lg"
              onClick={() => fileInputRef.current?.click()}
            >
              <span className="text-white text-sm">Change Photo</span>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleImageChange}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">First Name</label>
            <Input {...register("firstName")} />
          </div>
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Last Name</label>
            <Input {...register("lastName")} />
          </div>
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Email</label>
            <Input {...register("email")} type="email" />
          </div>
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Date of Birth</label>
            <Input {...register("dateOfBirth")} type="date" />
          </div>
        </div>

        {data?.referralCode ? (
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Referral Code</label>
            <Input value={data?.referralCode} disabled />
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            <Button
              type="button"
              disabled={isPending}
              onClick={() => mutate()}
              className="w-full"
            >
              Get Referral Code
            </Button>
          </div>
        )}

        {data?.profile.documents.length > 0 ? (
          <div className="grid grid-cols-1 gap-4">
            {data?.profile.documents.map((document: any) => (
              <div
                key={document.id}
                className="flex items-center p-3 bg-green-50 rounded-lg"
              >
                <FileText className="h-6 w-6 text-green-500 mr-2" />
                <p className="text-sm text-green-700">{document.name}</p>
                <span className="ml-auto text-xs text-green-600">
                  ✓ Uplaoded
                </span>
              </div>
            ))}
          </div>
        ) : (
          <div className="rounded-xl bg-gray-50 p-6 flex gap-y-4 flex-col justify-center items-center">
            <div className="text-center">
              <h1 className="text-lg text-red-600">No documents uploaded</h1>
              <p className="text-sm text-gray-600">
                Your account must have required documents uploaded to be
                verified and start processes
              </p>
            </div>
            <Button type="button" onClick={() => setuploadFile(!uploadFile)}>
              Upload your documents
            </Button>
          </div>
        )}

        {data?.profile.documents?.length == 0 && (
          <DocumentsModal
            isOpen={uploadFile}
            onClose={() => setuploadFile(!uploadFile)}
          />
        )}

        <div className="flex justify-end">
          <Button type="submit" className="px-6">
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  );
}
