/* eslint-disable @next/next/no-img-element */
"use client";
import { LoadingSpinner } from "@/components/loading-spinner";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { SingleAgent } from "@/types/agents";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { Mail, Phone, Power, Pencil, Trash2 } from "lucide-react";
import { useParams } from "next/navigation";

export default function AgentDetailsPage() {
  const { id } = useParams();
  const { data, isLoading } = useQuery<SingleAgent>({
    queryKey: ["agent", id],
    queryFn: async () => (await axios.get(`/api/v1/agents/${id}`)).data,
  });

  if (isLoading) return <LoadingSpinner text="Loading..." />;

  return (
    <div>
      <div className="flex flex-col items-center gap-y-1">
        <Badge variant="outline" className="mb-2">
          {data?.user.role}
        </Badge>
        <div className="w-32 h-32 rounded-full overflow-hidden mb-4">
          <img
            src={data?.user.profile.avatar}
            alt={data?.user.name}
            className="w-full h-full object-cover"
          />
        </div>
        <h1 className="text-2xl font-bold">{data?.user.name}</h1>
        <div className="flex items-center gap-2">
          <Phone />
          <p>{data?.user.phone}</p>
        </div>
        <div className="flex items-center gap-2">
          <Mail />
          <p>{data?.user.email}</p>
        </div>
        <div>
          <p>
            {data?.user.profile.verified ? (
              <Badge variant="secondary">Verified</Badge>
            ) : (
              <Badge variant="destructive">Not Verified</Badge>
            )}
          </p>
        </div>
      </div>

      <div className="mt-8 flex flex-col items-center">
        <h2 className="text-xl font-bold">Bio Info</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Joinned At</p>
            <p>
              {new Date(data?.user.createdAt || "").toLocaleDateString(
                "en-US",
                {
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                }
              )}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Referral Code</p>
            <p>{data?.user.referralCode}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Date of Birth</p>
            <p>
              {new Date(
                data?.user.profile.dateOfBirth || ""
              ).toLocaleDateString("en-US", {
                month: "long",
                day: "numeric",
                year: "numeric",
              })}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Agency</p>
            <p>{data?.agency.name}</p>
          </div>
        </div>
      </div>
      <div className="mt-8 flex flex-col items-center">
        <h2 className="text-xl font-bold">Documents</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {data?.user.profile.documents.map((document) => (
            <div key={document.id}>
              <p className="text-sm text-muted-foreground">{document.name}</p>
              <p>{document.frontSide}</p>
            </div>
          ))}
        </div>
      </div>
      <div className="mt-8 flex flex-col items-center">
        <h2 className="text-xl font-bold">Basic Stats</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Total Applicants</p>
            <p>{data?.user.applicants.length}</p>
          </div>
        </div>
      </div>

      <div className="mt-8 flex flex-col items-center">
        <h2 className="text-xl font-bold">Actions</h2>
        <div className="flex gap-4 mt-4">
          <Button
            variant="destructive"
            onClick={() => {
              // Add delete mutation logic here
            }}
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
          <Button
            variant="outline"
            onClick={() => {
              // Add activate/deactivate mutation logic here
            }}
          >
            <Power className="mr-2 h-4 w-4" />
            {data?.user.activated ? "Deactivate" : "Activate"}
          </Button>
          <Button
            variant="secondary"
            onClick={() => {
              // Add update logic here
            }}
          >
            <Pencil className="mr-2 h-4 w-4" />
            Update
          </Button>
        </div>
      </div>
    </div>
  );
}
