"use client";
import AgentModal from "@/components/modals/agents";
import AgentsTable from "@/components/tables/agents";
import { useState } from "react";

export default function Agents() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div>
      <h1 className="text-2xl font-bold mb-4">Agents</h1>

      <AgentsTable onAdd={() => setIsOpen(true)} />
      <AgentModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </div>
  );
}
