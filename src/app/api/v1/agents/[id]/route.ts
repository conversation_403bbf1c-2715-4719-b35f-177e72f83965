import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Params) {
  try {
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { message: "Agent ID is required" },
        { status: 400 }
      );
    }
    const agent = await prisma.agent.findUnique({
      where: { id },
      select: {
        id: true,
        agency: {
          select: {
            name: true,
          },
        },
        agencyId: true,
        createdAt: true,
        updatedAt: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            role: true,
            activated: true,
            referralCode: true,
            createdAt: true,
            applicants: {
              select: {
                id: true,
                clientFullName: true,
                clientEmail: true,
                clientPhone: true,
                createdAt: true,
                updatedAt: true,
              },
            },
            updatedAt: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true,
                verified: true,
                dateOfBirth: true,
                documents: {
                  select: {
                    id: true,
                    name: true,
                    frontSide: true,
                    backSide: true,
                  },
                },
              },
            },
          },
        },
      },
    });
    return NextResponse.json(agent);
  } catch (error) {
    console.error("Error fetching agent:", error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const session = await auth();
    const { id } = params;

    if (
      !session ||
      (session.user.role !== "GENERAL_MANAGER" &&
        session.user.role !== "AGENCY_MANAGER")
    ) {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    if (!id) {
      return NextResponse.json(
        { message: "Agent ID is required" },
        { status: 400 }
      );
    }

    const agent = await prisma.agent.delete({
      where: { id },
    });
    return NextResponse.json(agent);
  } catch (error) {
    console.error("Error deleting agent:", error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}
