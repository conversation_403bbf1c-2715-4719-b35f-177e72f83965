import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { agentSchema } from "@/lib/schemas";
import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import { uploadImage } from "@/lib/cloudinary";

export async function GET(request: NextRequest) {
  try {
    const agents = await prisma.agent.findMany({
      include: {
        agency: {
          select: {
            name: true,
          },
        },
        user: {
          select: {
            name: true,
            email: true,
            phone: true,
            role: true,
            activated: true,
            referralCode: true,
            createdAt: true,
            updatedAt: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true,
                verified: true,
              },
            },
          },
        },
      },
    });
    return NextResponse.json(agents);
  } catch (error) {
    return NextResponse.json(error, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user.role !== "GENERAL_MANAGER") {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const valid = agentSchema.safeParse(data);
    if (!valid.success) {
      return NextResponse.json(valid.error, { status: 400 });
    }
    const existingAgent = await prisma.user.findUnique({
      where: {
        email: valid.data.email,
      },
    });

    if (existingAgent) {
      return NextResponse.json(
        { message: "Agent already exists" },
        { status: 400 }
      );
    }

    let idCardFile;
    let locationPlacardFile;
    if (valid.data.idCardFile) {
      idCardFile = await uploadImage(valid.data.idCardFile);
    }
    if (valid.data.locationPlacardFile) {
      locationPlacardFile = await uploadImage(valid.data.locationPlacardFile);
    }

    const hash = await bcrypt.hash(valid.data.password, 10);
    const agent = await prisma.user.create({
      data: {
        name: valid.data.username,
        email: valid.data.email,
        phone: valid.data.phoneNumber,
        password: hash,
        role: "AGENT",
      },
    });

    await prisma.profile.create({
      data: {
        userId: agent.id,
        firstName: valid.data.fullName.split(" ")[0],
        lastName: valid.data.fullName.split(" ")[1],
        avatar: "https://avatar.iran.liara.run/public",
      },
    });

    await prisma.agent.create({
      data: {
        userId: agent.id,
        agencyId: valid.data.agencyId,
      },
    });

    if (idCardFile && locationPlacardFile) {
      await prisma.document.create({
        data: {
          name: "Location Plan",
          frontSide: locationPlacardFile.url,
          profile: {
            connect: {
              userId: agent.id,
            },
          },
        },
      });

      await prisma.document.create({
        data: {
          name: "ID Card",
          frontSide: idCardFile.url,
          profile: {
            connect: {
              userId: agent.id,
            },
          },
        },
      });
    }
    return NextResponse.json(agent);
  } catch (error) {
    console.log(error);
    return NextResponse.json(error, { status: 500 });
  }
}
