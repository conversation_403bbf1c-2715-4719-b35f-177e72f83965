import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized access required" },
        { status: 401 }
      );
    }
    const tasks = await prisma.taskboard.findUnique({
      where: {
        userId: session.user.id,
      },
      include: {
        columns: {
          include: {
            tasks: {
              select: {
                id: true,
                title: true,
                description: true,
                assignedUsers: {
                  select: {
                    id: true,
                    status: true,
                    user: {
                      select: {
                        id: true,
                        name: true,
                        profile: {
                          select: {
                            avatar: true,
                            firstName: true,
                            lastName: true,
                          },
                        },
                      },
                    },
                  },
                },
                status: true,
                isArchive: true,
                createdAt: true,
                priority: true,
                dueDate: true,
                comments: true,
              },
            },
          },
        },
      },
    });
    return NextResponse.json(tasks);
  } catch (error) {
    return NextResponse.json(error, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized access required" },
        { status: 401 }
      );
    }

    // Check if board already exists
    const existingBoard = await prisma.taskboard.findUnique({
      where: {
        userId: session.user.id,
      },
    });

    if (existingBoard) {
      return NextResponse.json(
        { message: "Board already exists" },
        { status: 400 }
      );
    }

    // Create new board with default columns
    const board = await prisma.taskboard.create({
      data: {
        userId: session.user.id,
        columns: {
          create: [
            {
              title: "Backlog",
              order: 1,
              tasks: {
                create: [
                  {
                    title: "Create Task Board",
                    description: "Create a task board for users",
                    status: "todo",
                    priority: "high",
                    dueDate: new Date(),
                  },
                ],
              },
            },
            {
              title: "To Do",
              order: 2,
              tasks: {
                create: [
                  {
                    title: "Create Task Board",
                    description: "Create a task board for users",
                    status: "todo",
                    priority: "high",
                    dueDate: new Date(),
                  },
                ],
              },
            },
            {
              title: "In Progress",
              order: 3,
              tasks: {
                create: [
                  {
                    title: "Create Task Board",
                    description: "Create a task board for users",
                    status: "todo",
                    priority: "high",
                    dueDate: new Date(),
                  },
                ],
              },
            },
            {
              title: "Done",
              order: 4,
              tasks: {
                create: [
                  {
                    title: "Create Task Board",
                    description: "Create a task board for users",
                    status: "todo",
                    priority: "high",
                    dueDate: new Date(),
                  },
                ],
              },
            },
            {
              title: "Blocked",
              order: 5,
              tasks: {
                create: [
                  {
                    title: "Create Task Board",
                    description: "Create a task board for users",
                    status: "todo",
                    priority: "high",
                    dueDate: new Date(),
                  },
                ],
              },
            },
          ],
        },
      },
      include: {
        columns: true,
      },
    });

    return NextResponse.json(board);
  } catch (error) {
    console.error("Error creating board:", error);
    return NextResponse.json(
      { message: "Failed to create board" },
      { status: 500 }
    );
  }
}
