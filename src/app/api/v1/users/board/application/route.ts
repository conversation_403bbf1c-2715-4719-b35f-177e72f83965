import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { message: "Unauthorized access required" },
        { status: 401 }
      );
    }

    const userBoard = await prisma.board.findFirst({
      include: {
        states: {
          include: {
            applicants: {
              where: { userId: session.user.id },
              select: {
                user: {
                  select: {
                    name: true,
                    email: true,
                    profile: {
                      select: {
                        avatar: true,
                        firstName: true,
                        lastName: true,
                      },
                    },
                  },
                },
                updatedAt: true,
                createdAt: true,
                userInfo: true,
                associates: true,
                code: true,
                address: true,
                clientEmail: true,
                clientFullName: true,
                clientPhone: true,
                comments: {
                  select: {
                    user: {
                      select: {
                        profile: {
                          select: {
                            avatar: true,
                            firstName: true,
                            lastName: true,
                          },
                        },
                      },
                    },
                  },
                },
                currentPayment: true,
                documents: true,
                dueDate: true,
                source: true,
                priority: true,
                fee: true,
                country: {
                  select: {
                    code: true,
                    flag: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return NextResponse.json(userBoard);
  } catch (error) {
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Error fetching your board",
      },
      { status: 500 }
    );
  }
}
