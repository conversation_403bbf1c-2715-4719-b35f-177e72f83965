import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { uploadFile } from "@/lib/cloudinary";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized access required" },
        { status: 401 }
      );
    }
    const data = await request.formData();
    const idCardFront = data.get("idCardFront") as File | null;
    const idCardBack = data.get("idCardBack") as File | null;
    const locationPlan = data.get("locationPlan") as File | null;

    if (!idCardFront || !idCardBack || !locationPlan) {
      return NextResponse.json(
        { message: "All required documents must be provided" },
        { status: 400 }
      );
    }
    const idCardFrontUrl = await uploadFile(idCardFront);
    const idCardBackUrl = await uploadFile(idCardBack);
    const locationPlanUrl = await uploadFile(locationPlan);

    await prisma.document.create({
      data: {
        name: "ID Card",
        frontSide: idCardFrontUrl.url,
        backSide: idCardBackUrl.url,
        profile: {
          connect: {
            userId: session.user.id,
          },
        },
      },
    });

    await prisma.document.create({
      data: {
        name: "Location Plan",
        frontSide: locationPlanUrl.url,
        profile: {
          connect: {
            userId: session.user.id,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Documents uploaded successfully",
      success: true,
    });
  } catch (error) {
    console.error("Error uploading documents:", error);
    return NextResponse.json(
      { message: "Failed to upload documents" },
      { status: 500 }
    );
  }
}
