import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized access required" },
        { status: 401 }
      );
    }
    const code = `code-${Math.floor(Math.random() * 1000000)}`;
    const user = await prisma.user.update({
      where: { id: session.user.id },
      data: { referralCode: code },
    });
    return NextResponse.json(user);
  } catch (error) {
    return NextResponse.json(error, { status: 500 });
  }
}
