import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized  access required" },
        { status: 401 }
      );
    }
    const users = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
      select: {
        name: true,
        email: true,
        phone: true,
        role: true,
        activated: true,
        referralCode: true,
        createdAt: true,
        updatedAt: true,
        profile: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true,
            verified: true,
            dateOfBirth: true,
            documents: {
              select: {
                id: true,
                name: true,
                frontSide: true,
                backSide: true,
              },
            },
          },
        },
      },
    });
    return NextResponse.json(users);
  } catch (error) {
    return NextResponse.json(error, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { message: "Unauthorized access required" },
        { status: 401 }
      );
    }

    const data = await request.formData();
    const documents = data.get("documents") as File[] | null;

    if (session.user.role !== "GENERAL_MANAGER") {
      // Non-admin users can only update documents
      const user = await prisma.user.update({
        where: {
          id: session.user.id,
        },
        data: {
          profile: {
            update: {
              documents: {
                // update: data.documents,
              },
            },
          },
        },
        select: {
          profile: {
            select: {
              documents: true,
            },
          },
        },
      });
      return NextResponse.json(user);
    }

    // Admin can update all fields
    // const user = await prisma.user.update({
    //   where: {
    //     // id: data.id,
    //   },
    //   data,
    // });
    // return NextResponse.json(user);
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { message: "Failed to update user" },
      { status: 500 }
    );
  }
}
