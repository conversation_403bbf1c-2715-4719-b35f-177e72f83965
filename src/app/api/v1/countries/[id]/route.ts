import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

// Refactor Params to better handle incoming parameters
interface Params {
  params: Promise<{
    id: string;
  }>;
}

export async function DELETE(req: NextRequest, { params }: Params) {
  try {
    const { id } = await params;
    if (!id)
      return NextResponse.json({ message: "ID is required" }, { status: 400 });

    const country = await prisma.country.delete({ where: { id } });

    return NextResponse.json(
      {
        data: country,
        message: "Country deleted successfully",
        success: true,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error(error);

    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Something went wrong",
      },
      { status: 500 }
    );
  }
}
