import { prisma } from "@/lib/prisma";
import axios from "axios";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const countries = await prisma.country.findMany();
    return NextResponse.json(countries);
  } catch (error) {
    return NextResponse.json(error, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name } = await request.json();
    if (!name) {
      return NextResponse.json(
        { message: "Name is required" },
        { status: 400 }
      );
    }
    const existingCountry = await prisma.country.findUnique({
      where: {
        name,
      },
    });
    if (existingCountry) {
      return NextResponse.json(
        { message: "Country already exists" },
        { status: 400 }
      );
    }
    const {
      data: [apiCountry],
    } = await axios.get(
      `https://restcountries.com/v3.1/name/${name.toLowerCase()}`
    );

    const countryData = {
      name: apiCountry.name.common,
      code: apiCountry.cca2,
      capital: apiCountry.capital?.[0],
      currency:
        apiCountry.currencies[Object.keys(apiCountry.currencies)[0]].name,
      timezone: apiCountry.timezones?.[0],
      population: apiCountry.population,
      flag: apiCountry.flags.png,
    };
    const country = await prisma.country.create({
      data: countryData,
      select: {
        id: true,
        name: true,
        code: true,
        capital: true,
        currency: true,
        timezone: true,
        population: true,
        applications: true,
        flag: true,
      },
    });
    return NextResponse.json(
      {
        data: country,
        message: "Country created successfully",
        success: true,
      },
      { status: 201 }
    );
  } catch (error) {
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : "Something went wrong",
      },
      { status: 500 }
    );
  }
}
