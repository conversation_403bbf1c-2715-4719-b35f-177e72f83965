/* eslint-disable @typescript-eslint/no-explicit-any */

import { NextResponse } from "next/server";
import { auth } from "@/auth";
import { prisma as db } from "@/lib/prisma";

export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session?.user?.role || session.user.role !== "GENERAL_MANAGER") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!session.user.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const data = await request.json();
    // Check if country exists
    const country = await db.country.findUnique({
      where: { id: data.countryId },
    });

    if (!country) {
      return NextResponse.json({ error: "Country not found" }, { status: 404 });
    }

    const form = await db.form.create({
      data: {
        title: data.title || "Untitled Form",
        description: data.description,
        country: {
          connect: {
            id: country.id,
          },
        },
        fields: {
          create: data.fields.map((field: any, index: number) => ({
            key: field.key,
            label: field.label,
            type: field.type,
            required: field.required || false,
            options: field.options || [],
            placeholder: field.placeholder,
            helpText: field.helpText,
            isArray: field.isArray || false,
            arrayConfig: field.arrayConfig || { minItems: 0, maxItems: null },
            validation: field.validation || {},
            defaultValue: field.defaultValue,
            order: field.order !== undefined ? field.order : index,
          })),
        },
      },
      include: {
        fields: true,
        country: true,
      },
    });

    return NextResponse.json(form);
  } catch (error) {
    console.error("Form creation error:", error);
    return NextResponse.json(
      { error: "Failed to create form" },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // const published = searchParams.get("published");

    const forms = await db.form.findMany({
      orderBy: {
        updatedAt: "desc",
      },
      include: {
        fields: true,
        country: true,
      },
    });

    return NextResponse.json(forms);
  } catch (error) {
    console.error("Get forms error:", error);
    return NextResponse.json(
      { error: "Failed to fetch forms" },
      { status: 500 }
    );
  }
}
