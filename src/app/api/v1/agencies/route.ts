import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { agencySchema } from "@/lib/schemas";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const agencies = await prisma.agency.findMany({
      include: {
        agents: true,
        manager: true,
      },
    });
    return NextResponse.json(agencies);
  } catch (error) {
    return NextResponse.json(error, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user.role !== "GENERAL_MANAGER") {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const valid = agencySchema.safeParse(data);
    if (!valid.success) {
      return NextResponse.json(valid.error, { status: 400 });
    }
    const existingAgency = await prisma.agency.findUnique({
      where: {
        email: valid.data.email,
      },
    });

    if (existingAgency) {
      return NextResponse.json(
        { message: "Agency already exists" },
        { status: 400 }
      );
    }

    const agency = await prisma.agency.create({
      data: {
        name: valid.data.name,
        location: valid.data.location,
        email: valid.data.email,
        phone: valid.data.phone,
      },
    });
    return NextResponse.json(agency);
  } catch (error) {
    console.log(error);
    return NextResponse.json(error, { status: 500 });
  }
}
