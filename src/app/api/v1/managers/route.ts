import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { mangerSchema } from "@/lib/schemas";
import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session || session.user.role !== "GENERAL_MANAGER") {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }
    const managers = await prisma.agencyManager.findMany({
      include: {
        agency: {
          select: {
            name: true,
            location: true,
          },
        },
        user: {
          select: {
            name: true,
            email: true,
            phone: true,
            role: true,
            activated: true,
            referralCode: true,
            createdAt: true,
            updatedAt: true,
            profile: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true,
                verified: true,
              },
            },
          },
        },
      },
    });
    return NextResponse.json(managers);
  } catch (error) {
    return NextResponse.json(error, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user.role !== "GENERAL_MANAGER") {
      return NextResponse.json(
        { message: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const data = await request.json();
    const valid = mangerSchema.safeParse(data);
    if (!valid.success) {
      return NextResponse.json(valid.error, { status: 400 });
    }
    const existingManager = await prisma.user.findUnique({
      where: {
        email: valid.data.email,
      },
    });

    if (existingManager) {
      return NextResponse.json(
        { message: "Manager already exists" },
        { status: 400 }
      );
    }

    const hash = await bcrypt.hash(valid.data.password, 10);
    const manager = await prisma.user.create({
      data: {
        name: valid.data.name,
        email: valid.data.email,
        phone: valid.data.phone,
        password: hash,
        role: "AGENCY_MANAGER",
      },
    });

    await prisma.profile.create({
      data: {
        userId: manager.id,
        firstName: valid.data.name.split(" ")[0],
        lastName: valid.data.name.split(" ")[1],
        avatar: "https://avatar.iran.liara.run/public",
      },
    });

    await prisma.agencyManager.create({
      data: {
        userId: manager.id,
        agencyId: valid.data.agencyId,
      },
    });

    return NextResponse.json(manager);
  } catch (error) {
    console.log(error);
    return NextResponse.json(error, { status: 500 });
  }
}
