import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";
import { uploadImage } from "@/lib/cloudinary";
import { companySchema } from "@/lib/schemas";
import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";

export async function POST(request: NextRequest) {
  const data = await request.formData();
  const logo = data.get("logo") as File;
  const name = data.get("name");
  const email = data.get("email");
  const phone = data.get("phone");
  const address = data.get("address");
  const agencyName = data.get("agencyName");
  const managerName = data.get("managerName");
  const managerEmail = data.get("managerEmail");
  const managerPhone = data.get("managerPhone");
  const managerPassword = data.get("managerPassword");
  const managerConfirmPassword = data.get("managerConfirmPassword");
  const payload = {
    logo,
    name,
    email,
    phone,
    address,
    agencyName,
    managerName,
    managerEmail,
    managerPhone,
    managerPassword,
    managerConfirmPassword,
  };

  try {
    const valid = companySchema.safeParse(payload);
    if (!valid.success) {
      return NextResponse.json(valid.error, { status: 400 });
    }
    let logo;
    // upload logo
    if (payload.logo) logo = await uploadImage(payload.logo);
    const companyExists = await prisma.company.findUnique({
      where: {
        name: valid.data.name,
      },
    });
    if (companyExists) {
      return NextResponse.json(
        { message: "Company already exists" },
        { status: 400 }
      );
    }

    // create company
    const company = await prisma.company.create({
      data: {
        name: valid.data.name,
        logo: logo?.url || "",
        isSetupCompleted: true,
      },
    });

    // create agency
    const agency = await prisma.agency.create({
      data: {
        name: valid.data.agencyName,
        location: valid.data.address,
        isHeadOffice: true,
        companyId: company.id,
        email: valid.data.email,
        phone: valid.data.phone,
      },
    });

    const hash = await bcrypt.hash(valid.data.managerPassword, 10);
    // create manager
    const manager = await prisma.user.create({
      data: {
        name: valid.data.managerName,
        email: valid.data.managerEmail,
        phone: valid.data.managerPhone,
        password: hash,
        role: "GENERAL_MANAGER",
      },
    });

    await prisma.profile.create({
      data: {
        userId: manager.id,
        firstName: valid.data.managerName.split(" ")[0],
        lastName: valid.data.managerName.split(" ")[1],
        avatar: "https://avatar.iran.liara.run/public",
      },
    });

    await prisma.generalManager.create({
      data: {
        userId: manager.id,
      },
    });

    const setupData = {
      setupCompleted: true,
      completedAt: new Date().toISOString(),
      company: {
        id: company.id,
        name: company.name,
      },
      agency: {
        id: agency.id,
        name: agency.name,
      },
      manager: {
        id: manager.id,
        name: manager.name,
        email: manager.email,
      },
    };

    // Create the setup.json file in the root directory or a config folder
    const setupFilePath = path.join(process.cwd(), "setup.json");
    fs.writeFileSync(setupFilePath, JSON.stringify(setupData, null, 2));

    return NextResponse.json({ company, agency, manager });
  } catch (error) {
    console.log(error);
    return NextResponse.json(error, { status: 500 });
  }
}
