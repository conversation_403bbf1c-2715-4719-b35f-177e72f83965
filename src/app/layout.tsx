import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import { Toaster } from "@/components/ui/sonner";
import { ReactQueryProvider } from "@/components/react-query";
import Setup from "@/guards/setup";
const geistSans = Poppins({
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.className} antialiased`}>
        <ReactQueryProvider>
          <Setup onboardingPath="/onboarding" redirectPath="/">
            {children}
            <Toaster position="bottom-right" richColors />
          </Setup>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
