"use client";
import FormPreview from "@/components/forms/form-preview";
import { LoadingSpinner } from "@/components/loading-spinner";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useParams } from "next/navigation";
import React from "react";

export default function SingleForm() {
  const { id } = useParams();

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["form", id],
    queryFn: async () => {
      const res = await axios.get(`/api/v1/forms/${id}`);
      return res.data;
    },
  });

  if (isLoading) return <LoadingSpinner text="Loading Info" />;

  if (isError) return <div>{error.message}</div>;

  return (
    <div>
      <FormPreview
        key={data.id}
        type="apply"
        fields={data.fields}
        description={data.description}
        title={data.title}
        country={data.country}
        onSubmit={() => {}}
        isSubmitting={false}
      />
    </div>
  );
}
