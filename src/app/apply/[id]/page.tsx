"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { ApplicantInfo, applicantInfoSchema } from "@/lib/schemas/users-info";
import { useParams, useRouter } from "next/navigation";

export default function ClientContactInfo() {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(applicantInfoSchema),
  });

  const router = useRouter();
  const params = useParams();

  const onSubmit = (data: ApplicantInfo) => {
    router.push(`/apply/${params.id}/form`);

    console.log(data);
  };

  const sources = [
    "Social Media",
    "Website",
    "Referral",
    "Advertisement",
    "Other",
  ];

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8 bg-white p-8 rounded-lg shadow-lg">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-gray-900 mb-2">
            Contact Information
          </h2>
          <p className="text-gray-600 mb-8">
            Please fill out your contact details below. This information will be
            used to process your application and keep you updated about its
            status.
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
              <span className="text-xs text-gray-500 ml-2">
                (as it appears on official documents)
              </span>
            </label>
            <Input {...register("fullName")} className="rounded-md" />
            {errors.fullName && (
              <p className="text-red-500 text-sm mt-1">
                {errors.fullName.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
              <span className="text-xs text-gray-500 ml-2">
                (for important notifications)
              </span>
            </label>
            <Input type="email" {...register("email")} className="rounded-md" />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">
                {errors.email.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
              <span className="text-xs text-gray-500 ml-2">
                (include country code)
              </span>
            </label>
            <PhoneInput
              country={"us"}
              onChange={(phone) => setValue("phone", phone)}
              containerClass="w-full"
              inputStyle={{
                width: "100%",
                height: "42px",
                borderRadius: "6px",
              }}
              buttonStyle={{ borderRadius: "6px 0 0 6px" }}
            />
            {errors.phone && (
              <p className="text-red-500 text-sm mt-1">
                {errors.phone.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              How did you hear about us?
              <span className="text-xs text-gray-500 ml-2">
                (helps us improve our outreach)
              </span>
            </label>
            <Select onValueChange={(value) => setValue("source", value)}>
              <SelectTrigger className="rounded-md">
                <SelectValue placeholder="Select a source" />
              </SelectTrigger>
              <SelectContent>
                {sources.map((source) => (
                  <SelectItem key={source} value={source}>
                    {source}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.source && (
              <p className="text-red-500 text-sm mt-1">
                {errors.source.message}
              </p>
            )}
          </div>

          <div className="pt-4">
            <Button
              type="submit"
              className="w-full py-2 px-4 rounded-md hover:bg-blue-600 transition-colors"
            >
              Submit Application
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
