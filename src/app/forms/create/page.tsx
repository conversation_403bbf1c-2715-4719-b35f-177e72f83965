/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState } from "react";
import FormBuilder from "@/components/forms/form-temnplate";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { useRouter } from "next/navigation";
import { getCountries } from "@/actions/countries";
// import { AdvancedGridBackground } from "@/components/grid-bg";

interface FormTemplate {
  title: string;
  description: string;
  countryId: string;
  fields: any[]; // You might want to properly type this based on your Field type
}

export default function FormPage() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const { data: countries } = useQuery({
    queryKey: ["countries"],
    queryFn: getCountries,
  });

  const { mutate, isPending } = useMutation({
    mutationFn: async (formData: FormTemplate) => {
      const { data } = await axios.post("/api/form/", formData);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["formTemplates"] });
      router.push("/dashboard/forms");
    },
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Failed to save form template";
    },
  });

  const handleSave = (formData: FormTemplate) => {
    formData.countryId = selectedCountry || "";
    mutate(formData);
  };

  return (
    <div className="">
      <div>
        <FormBuilder
          saveFormValue={handleSave}
          initialFields={[]}
          countries={countries}
          selectedCountry={selectedCountry}
          setSelectedCountry={setSelectedCountry}
        />
        {isPending && (
          <div className="flex justify-center py-4">
            <span className="text-gray-500">Saving form template...</span>
          </div>
        )}
      </div>
    </div>
  );
}
