"use client";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { companySchema } from "@/lib/schemas/company";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useOnboardingStore } from "../store";
import { FormLayoutCard } from "@/components/form-layout-card";

const contactSchema = companySchema.pick({
  phone: true,
  email: true,
});

type Contact = z.infer<typeof contactSchema>;
export default function Contact() {
  const router = useRouter();
  const setData = useOnboardingStore((state) => state.setDate);

  const { register, handleSubmit, formState } = useForm<Contact>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      phone: "",
      email: "",
    },
  });

  const onSubmit = handleSubmit((data) => {
    setData(data);
    router.push("/onboarding/manager");
  });
  return (
    <div className="min-h-screen flex items-center justify-center">
      <FormLayoutCard
        title="Contact"
        description="Please enter your contact information."
      >
        <form onSubmit={onSubmit} className="w-[300px] flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <label htmlFor="phone" className="text-sm">
              Phone
            </label>
            <Input {...register("phone")} />
            {formState.errors.phone && (
              <span className="text-destructive text-sm">
                {formState.errors.phone.message}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-2">
            <label htmlFor="email" className="text-sm">
              Email
            </label>
            <Input {...register("email")} />
            {formState.errors.email && (
              <span className="text-destructive text-sm">
                {formState.errors.email.message}
              </span>
            )}
          </div>
          <Button type="submit" className="w-full">
            Next
          </Button>
        </form>
      </FormLayoutCard>
    </div>
  );
}
