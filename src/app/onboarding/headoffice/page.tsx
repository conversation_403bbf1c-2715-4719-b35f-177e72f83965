"use client";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { companySchema } from "@/lib/schemas/company";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useOnboardingStore } from "../store";
import { FormHeader } from "@/components/form-header";
import { FormLayoutCard } from "@/components/form-layout-card";

const headOfficeSchema = companySchema.pick({
  agencyName: true,
  address: true,
});

type HeadOffice = z.infer<typeof headOfficeSchema>;
export default function HeadOffice() {
  const router = useRouter();
  const setData = useOnboardingStore((state) => state.setDate);
  const { register, handleSubmit, formState } = useForm<HeadOffice>({
    resolver: zodResolver(headOfficeSchema),
  });
  const onSubmit = handleSubmit((data) => {
    setData(data);
    router.push("/onboarding/contact");
  });
  return (
    <div className="min-h-screen flex items-center justify-center">
      <FormLayoutCard
        title="Head Office"
        description="Please enter your Head Office information."
      >
        <form onSubmit={onSubmit} className="w-[300px] flex flex-col gap-4">
          <div className="flex flex-col gap-2 w-full">
            <label htmlFor="agencyName" className="text-sm">
              Name
            </label>
            <Input {...register("agencyName")} className="w-full" />
            {formState.errors.agencyName && (
              <span className="text-destructive text-sm">
                {formState.errors.agencyName.message}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-2 w-full">
            <label htmlFor="address" className="text-sm">
              Address
            </label>
            <Input {...register("address")} className="w-full" />
            {formState.errors.address && (
              <span className="text-destructive text-sm">
                {formState.errors.address.message}
              </span>
            )}
          </div>
          <Button type="submit" className="w-full">
            Next
          </Button>
        </form>
      </FormLayoutCard>
    </div>
  );
}
