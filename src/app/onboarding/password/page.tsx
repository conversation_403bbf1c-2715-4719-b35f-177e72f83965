"use client";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { Company, companySchema } from "@/lib/schemas/company";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import { useOnboardingStore } from "../store";
import { FormLayoutCard } from "@/components/form-layout-card";
import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const passwordSchema = companySchema.pick({
  managerPassword: true,
  managerConfirmPassword: true,
});

type Password = z.infer<typeof passwordSchema>;

export default function Password() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const companyName = useOnboardingStore((state) => state.name);
  const managerLogo = useOnboardingStore((state) => state.logo);
  const agencyEmail = useOnboardingStore((state) => state.email);
  const agencyPhone = useOnboardingStore((state) => state.phone);
  const agencyName = useOnboardingStore((state) => state.agencyName);
  const agencyAddress = useOnboardingStore((state) => state.address);
  const managerName = useOnboardingStore((state) => state.managerName);
  const managerEmail = useOnboardingStore((state) => state.managerEmail);
  const managerPhone = useOnboardingStore((state) => state.managerName);

  const mutation = useMutation({
    mutationFn: (data: Company) => {
      const formData = new FormData();
      formData.append("logo", data.logo);
      formData.append("name", data.name);
      formData.append("email", data.email);
      formData.append("phone", data.phone);
      formData.append("address", data.address);
      formData.append("agencyName", data.agencyName);
      formData.append("managerName", data.managerName);
      formData.append("managerEmail", data.managerEmail);
      formData.append("managerPhone", data.managerPhone);
      formData.append("managerPassword", data.managerPassword);
      formData.append("managerConfirmPassword", data.managerConfirmPassword);
      return axios.post("/api/v1/company", formData);
    },
    onSuccess: () => {
      toast.success("Company created successfully");
      router.push("/login");
    },
    onError: () => {
      toast.error("Something went wrong");
    },
  });
  const { register, handleSubmit, formState } = useForm<Password>({
    resolver: zodResolver(passwordSchema),
  });

  const onSubmit = handleSubmit((data) => {
    console.log(managerLogo);

    const company = {
      ...data,
      name: companyName,
      email: agencyEmail,
      phone: agencyPhone,
      logo: managerLogo[0],
      agencyName,
      address: agencyAddress,
      managerName,
      managerEmail,
      managerPhone,
    } as Company;

    mutation.mutate(company);
  });
  return (
    <div className="min-h-screen flex items-center justify-center">
      <FormLayoutCard
        title="Manager Password"
        description="Please enter your company manager password."
      >
        <form onSubmit={onSubmit} className="w-[300px] flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <label htmlFor="password" className="text-sm">
              Password
            </label>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                {...register("managerPassword")}
                className=" pr-10"
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-500" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-500" />
                )}
              </button>
            </div>
            {formState.errors.managerPassword && (
              <span className="text-destructive text-sm">
                {formState.errors.managerPassword.message}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-2">
            <label htmlFor="confirmPassword" className="text-sm">
              Confirm Password
            </label>
            <div className="relative">
              <Input
                type={showConfirmPassword ? "text" : "password"}
                {...register("managerConfirmPassword")}
                className=" pr-10"
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-500" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-500" />
                )}
              </button>
            </div>
            {formState.errors.managerConfirmPassword && (
              <span className="text-destructive text-sm">
                {formState.errors.managerConfirmPassword.message}
              </span>
            )}
          </div>
          <Button type="submit" className="w-full">
            Next
          </Button>
        </form>
      </FormLayoutCard>
    </div>
  );
}
