"use client";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { companySchema } from "@/lib/schemas/company";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState } from "react";
import Image from "next/image";
import { useOnboardingStore } from "./store";
import { FormLayoutCard } from "@/components/form-layout-card";

const nameSchema = companySchema.pick({ name: true, logo: true });
type Name = z.infer<typeof nameSchema>;

export default function Onboarding() {
  const router = useRouter();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const setData = useOnboardingStore((state) => state.setDate);
  const { register, handleSubmit, formState } = useForm<Name>({
    resolver: zodResolver(nameSchema),
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const onSubmit = handleSubmit((data) => {
    setData(data);
    router.push("/onboarding/headoffice");
  });

  return (
    <div className="min-h-screen flex items-center justify-center">
      <FormLayoutCard
        title="Company"
        description="Please enter your company information."
      >
        <form onSubmit={onSubmit} className=" flex flex-col gap-4">
          <div className="flex flex-col gap-2 w-full">
            <label htmlFor="logo" className="text-sm">
              Logo
            </label>
            <Input
              type="file"
              accept="image/*"
              {...register("logo")}
              onChange={handleFileChange}
              className=" w-full"
            />
            {previewUrl && (
              <div className="relative w-full h-20 mt-2">
                <Image
                  src={previewUrl}
                  alt="Logo preview"
                  fill
                  className="object-contain"
                />
              </div>
            )}
            {formState.errors.logo && (
              <span className="text-destructive text-sm">
                {formState.errors.logo.message as string}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-2 w-full">
            <label htmlFor="name" className="text-sm">
              Name
            </label>
            <Input {...register("name")} className=" w-full" />
            {formState.errors.name && (
              <span className="text-destructive text-sm">
                {formState.errors.name.message}
              </span>
            )}
          </div>
          <Button type="submit" className="w-full">
            Next
          </Button>
        </form>
      </FormLayoutCard>
    </div>
  );
}
