"use client";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { companySchema } from "@/lib/schemas/company";
import { z } from "zod";
import { useOnboardingStore } from "../store";
import { FormHeader } from "@/components/form-header";
import { FormLayoutCard } from "@/components/form-layout-card";

const managerSchema = companySchema.pick({
  managerName: true,
  managerPhone: true,
  managerEmail: true,
});

type Manager = z.infer<typeof managerSchema>;
export default function Manager() {
  const router = useRouter();
  const setData = useOnboardingStore((state) => state.setDate);

  const { register, handleSubmit, formState } = useForm<Manager>({
    resolver: zodResolver(managerSchema),
  });

  const onSubmit = handleSubmit((data) => {
    console.log(data);

    setData(data);
    router.push("/onboarding/password");
  });
  return (
    <div className="min-h-screen flex items-center justify-center">
      <FormLayoutCard
        title="Manager"
        description="Please enter your company manager information."
      >
        <form onSubmit={onSubmit} className="w-[300px] flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <label htmlFor="name" className="text-sm">
              Name
            </label>
            <Input {...register("managerName")} />
            {formState.errors.managerName && (
              <span className="text-destructive text-sm">
                {formState.errors.managerName.message}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-2">
            <label htmlFor="phone" className="text-sm">
              Phone
            </label>
            <Input {...register("managerPhone")} />
            {formState.errors.managerPhone && (
              <span className="text-destructive text-sm">
                {formState.errors.managerPhone.message}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-2">
            <label htmlFor="email" className="text-sm">
              Email
            </label>
            <Input {...register("managerEmail")} />
            {formState.errors.managerEmail && (
              <span className="text-destructive text-sm">
                {formState.errors.managerEmail.message}
              </span>
            )}
          </div>
          <Button type="submit" className="w-full">
            Next
          </Button>
        </form>
      </FormLayoutCard>
    </div>
  );
}
