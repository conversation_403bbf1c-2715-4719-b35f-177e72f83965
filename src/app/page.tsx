import Header from "@/components/modals/header";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

export default function Home() {
  return (
    <main className="container mx-auto pb-40">
      <Header />
      <div className="mt-40">
        <div className="w-[60%] flex flex-col justify-center items-center mx-auto">
          <Badge className="rounded-none">Eureka</Badge>
          <h1 className="text-6xl leading-normal text-center font-semibold">
            Eureka Immigration Agency Client Management
          </h1>

          <p className="text-lg text-gray-600 text-center mt-4 mb-8">
            Streamline your immigration journey with our comprehensive client
            management system. We help connect clients with experienced
            immigration agents for a smoother visa application process.
          </p>

          <div className="flex gap-x-4">
            <Button variant={"outline"}>Client</Button>
            <Button>Agent</Button>
          </div>
        </div>
      </div>

      <div className="h-[80vh] bg-black my-30"></div>

      <div className="mt-32">
        <h2 className="text-4xl font-semibold text-center mb-16">
          Key Features
        </h2>
        <div className="grid grid-cols-3 gap-8">
          <div className="text-center p-6">
            <div className="mb-4">📝</div>
            <h3 className="text-xl font-medium mb-2">Easy Documentation</h3>
            <p className="text-gray-600">
              Simplified document submission and tracking for visa applications
            </p>
          </div>
          <div className="text-center p-6">
            <div className="mb-4">👥</div>
            <h3 className="text-xl font-medium mb-2">Agent Matching</h3>
            <p className="text-gray-600">
              Connect with qualified immigration agents based on your needs
            </p>
          </div>
          <div className="text-center p-6">
            <div className="mb-4">📊</div>
            <h3 className="text-xl font-medium mb-2">Progress Tracking</h3>
            <p className="text-gray-600">
              Real-time updates on your application status and next steps
            </p>
          </div>
        </div>
      </div>

      <div className="mt-32">
        <h2 className="text-4xl font-semibold text-center mb-16">
          Frequently Asked Questions
        </h2>
        <Accordion type="single" collapsible className="w-[80%] mx-auto">
          <AccordionItem value="item-1">
            <AccordionTrigger>
              How does the agent matching process work?
            </AccordionTrigger>
            <AccordionContent>
              Our system matches you with immigration agents based on your
              specific visa requirements, preferred destination country, and
              other relevant criteria to ensure the best fit for your case.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-2">
            <AccordionTrigger>
              What types of visas do you handle?
            </AccordionTrigger>
            <AccordionContent>
              We support various visa types including student visas, work visas,
              permanent residency, family sponsorship, and business visas. Our
              agents are experienced in handling different visa categories.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-3">
            <AccordionTrigger>
              How long does the visa process typically take?
            </AccordionTrigger>
            <AccordionContent>
              Processing times vary depending on the visa type and destination
              country. Your assigned agent will provide you with an estimated
              timeline based on your specific case and current processing times.
            </AccordionContent>
          </AccordionItem>
          <AccordionItem value="item-4">
            <AccordionTrigger>
              What documents will I need to provide?
            </AccordionTrigger>
            <AccordionContent>
              Required documents vary by visa type but typically include
              identification, educational certificates, work experience proof,
              and financial documents. Your agent will provide a detailed
              checklist.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <div className="mt-32">
        <h2 className="text-4xl font-semibold text-center mb-16">
          Testimonials
        </h2>
        <div className="max-w-4xl mx-auto px-4">
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                <Card className="text-center p-6 h-full">
                  <CardHeader>
                    <div className="mb-4 text-2xl">💬</div>
                    <CardTitle className="text-xl font-medium">
                      John Doe
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      "Eureka made the visa application process much easier. The
                      agent was knowledgeable and provided excellent support."
                    </p>
                  </CardContent>
                </Card>
              </CarouselItem>
              <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                <Card className="text-center p-6 h-full">
                  <CardHeader>
                    <div className="mb-4 text-2xl">💬</div>
                    <CardTitle className="text-xl font-medium">
                      Jane Smith
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      "I was impressed with the agent's professionalism and the
                      transparency throughout the process."
                    </p>
                  </CardContent>
                </Card>
              </CarouselItem>
              <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                <Card className="text-center p-6 h-full">
                  <CardHeader>
                    <div className="mb-4 text-2xl">💬</div>
                    <CardTitle className="text-xl font-medium">
                      Bob Johnson
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      "The agent's expertise and guidance were invaluable. I
                      couldn't have done it without them."
                    </p>
                  </CardContent>
                </Card>
              </CarouselItem>
              <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                <Card className="text-center p-6 h-full">
                  <CardHeader>
                    <div className="mb-4 text-2xl">💬</div>
                    <CardTitle className="text-xl font-medium">
                      Sarah Wilson
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      "The platform made it easy to find the right agent for my
                      specific needs. Highly recommend their services!"
                    </p>
                  </CardContent>
                </Card>
              </CarouselItem>
              <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                <Card className="text-center p-6 h-full">
                  <CardHeader>
                    <div className="mb-4 text-2xl">💬</div>
                    <CardTitle className="text-xl font-medium">
                      Michael Chen
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      "Excellent communication and support throughout my visa
                      application. The process was smooth and stress-free."
                    </p>
                  </CardContent>
                </Card>
              </CarouselItem>
              <CarouselItem className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                <Card className="text-center p-6 h-full">
                  <CardHeader>
                    <div className="mb-4 text-2xl">💬</div>
                    <CardTitle className="text-xl font-medium">
                      Emily Rodriguez
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      "Professional service with clear guidance at every step.
                      Made my immigration journey much more manageable."
                    </p>
                  </CardContent>
                </Card>
              </CarouselItem>
            </CarouselContent>
            <CarouselPrevious className="hidden md:flex" />
            <CarouselNext className="hidden md:flex" />
          </Carousel>
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-32 bg-gray-50 border-t">
        <div className="container mx-auto px-4 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900">Eureka</h3>
              <p className="text-gray-600 text-sm">
                Streamlining immigration journeys with comprehensive client
                management solutions. Connecting clients with experienced
                immigration agents worldwide.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-400 hover:text-gray-600">
                  <span className="sr-only">Facebook</span>
                  📘
                </a>
                <a href="#" className="text-gray-400 hover:text-gray-600">
                  <span className="sr-only">Twitter</span>
                  🐦
                </a>
                <a href="#" className="text-gray-400 hover:text-gray-600">
                  <span className="sr-only">LinkedIn</span>
                  💼
                </a>
                <a href="#" className="text-gray-400 hover:text-gray-600">
                  <span className="sr-only">Instagram</span>
                  📷
                </a>
              </div>
            </div>

            {/* Services */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Services</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Visa Applications
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Agent Matching
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Document Management
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Progress Tracking
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Consultation Services
                  </a>
                </li>
              </ul>
            </div>

            {/* Support */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Support</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    FAQ
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Live Chat
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-600 hover:text-gray-900">
                    Documentation
                  </a>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Contact</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <span>📍</span>
                  <span>123 Immigration St, City, Country</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>📞</span>
                  <span>+1 (555) 123-4567</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>✉️</span>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>🕒</span>
                  <span>Mon-Fri: 9AM-6PM</span>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="mt-12 pt-8 border-t border-gray-200">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="text-sm text-gray-600">
                © 2024 Eureka Immigration Agency. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm">
                <a href="#" className="text-gray-600 hover:text-gray-900">
                  Privacy Policy
                </a>
                <a href="#" className="text-gray-600 hover:text-gray-900">
                  Terms of Service
                </a>
                <a href="#" className="text-gray-600 hover:text-gray-900">
                  Cookie Policy
                </a>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </main>
  );
}
