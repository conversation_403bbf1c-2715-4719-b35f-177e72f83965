/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ArrayFieldItem {
  id: string;
  title: string;
  type: FieldType;
  key: string;
  required: boolean;
  placeholder: string;
  helpText: string;
  options: FieldOption[];
  validation: ValidationRules;
  defaultValue: any;
}

export type FieldType =
  | "text"
  | "number"
  | "email"
  | "textarea"
  | "select"
  | "multiselect"
  | "radio"
  | "checkbox"
  | "date"
  | "time"
  | "datetime"
  | "file"
  | "array";

export interface FieldOption {
  label: string;
  value: string;
}

export interface ValidationRules {
  required?: boolean;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
}

export interface ArrayConfig {
  minItems: number;
  maxItems: number | null;
  itemType: FieldType;
  itemLabel: string;
  items: ArrayFieldItem[];
}

export interface Field {
  id: string;
  label: string;
  type: FieldType;
  key: string;
  required: boolean;
  placeholder: string;
  helpText: string;
  options: FieldOption[];
  order: number;
  isArray: boolean;
  arrayConfig: ArrayConfig;
  validation: ValidationRules;
  defaultValue: any;
}

export interface FormSubmission {
  id: string;
  formId: string;
  data: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  submittedBy: string | null;
  submittedByUser: {
    id: string;
    name: string | null;
    email: string | null;
    image: string | null;
  } | null;
}

export interface FormDataResponse {
  id: string;
  title: string;
  description: string;
  fields: Field[];
  submissions: FormSubmission[];
  createdAt: Date;
  updatedAt: Date;
  isPublished: boolean;
}

export interface FormData {
  title: string;
  description: string;
  fields: Field[];
}
