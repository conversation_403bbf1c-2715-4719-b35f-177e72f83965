export interface Manager {
  id: string;
  userId: string;
  permissions: null;
  agencyId: string;
  createdAt: string;
  updatedAt: string;
  agency: Agency;
  user: User;
}

interface User {
  name: string;
  email: string;
  phone: string;
  role: string;
  activated: boolean;
  referralCode: null;
  createdAt: string;
  updatedAt: string;
  profile: Profile;
}

interface Profile {
  firstName: string;
  lastName: string;
  avatar: string;
  verified: boolean;
}

interface Agency {
  name: string;
  location: string;
}

export interface FormatedManager {
  id: string;
  avatar: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  activated: boolean;
  agency: string;
  createdAt: string;
}
