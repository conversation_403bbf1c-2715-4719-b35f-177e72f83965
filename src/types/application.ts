// types/immigration.ts
export type ApplicationType =
  | "student_visa"
  | "work_permit"
  | "permanent_residence"
  | "citizenship"
  | "family_reunion"
  | "refugee_status"
  | "tourist_visa"
  | "business_visa";

export type ApplicationStatus =
  | "submitted"
  | "under_review"
  | "documents_requested"
  | "interview_scheduled"
  | "approved"
  | "rejected"
  | "pending_payment"
  | "completed";

export type Priority = "low" | "medium" | "high" | "urgent";

export type Country = {
  code: string;
  name: string;
  flag: string;
};

export interface Applicant {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  nationality: Country;
  dateOfBirth: string;
  avatar?: string;
}

export interface CaseOfficer {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  initials: string;
}

export interface Application {
  id: string;
  applicationNumber: string;
  applicationType: ApplicationType;
  status: ApplicationStatus;
  applicant: Applicant;
  caseOfficer?: CaseOfficer;
  submittedDate: string;
  lastUpdated: string;
  dueDate?: string;
  priority: Priority;
  documentsCount: number;
  notesCount: number;
  isUrgent: boolean;
  estimatedProcessingTime: number; // in days
  currentStage: string;
  completionPercentage: number;
  fees: {
    total: number;
    paid: number;
    currency: string;
  };
}

export interface ApplicationColumn {
  id: string;
  title: string;
  status: ApplicationStatus;
  applicationIds: string[];
  color: string;
  order: number;
}

export interface ApplicationBoard {
  columns: ApplicationColumn[];
  applications: Record<string, Application>;
  columnOrder: string[];
}

export interface FilterOptions {
  applicationType: ApplicationType[];
  priority: Priority[];
  caseOfficer: string[];
  nationality: string[];
  dateRange: {
    from?: Date;
    to?: Date;
  };
  isUrgent?: boolean;
}

export interface SearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  applicationTypes: ApplicationType[];
  caseOfficers: CaseOfficer[];
  nationalities: Country[];
}

export interface ApplicationCardProps {
  application: Application;
  index: number;
  onUpdate?: (applicationId: string, updates: Partial<Application>) => void;
  onDelete?: (applicationId: string) => void;
}

export interface ApplicationColumnProps {
  column: ApplicationColumn;
  applications: Application[];
  onAddApplication?: (columnId: string) => void;
  onDeleteColumn?: (columnId: string) => void;
  onUpdateColumn?: (
    columnId: string,
    updates: Partial<ApplicationColumn>
  ) => void;
}

export interface ImmigrationBoardProps {
  initialData?: ApplicationBoard;
  onApplicationUpdate?: (
    applicationId: string,
    updates: Partial<Application>
  ) => void;
  onApplicationMove?: (
    applicationId: string,
    fromColumnId: string,
    toColumnId: string
  ) => void;
}
