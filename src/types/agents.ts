/* eslint-disable @typescript-eslint/no-explicit-any */
export interface Agent {
  id: string;
  userId: string;
  agencyId: string;
  createdAt: string;
  updatedAt: string;
  agency: Agency;
  user: User;
}

interface User {
  name: string;
  email: string;
  phone: string;
  role: string;
  activated: boolean;
  referralCode: null;
  createdAt: string;
  updatedAt: string;
  profile: Profile;
}

interface Profile {
  firstName: string;
  lastName: string;
  avatar: string;
  verified: boolean;
}

interface Agency {
  name: string;
}

export interface SingleAgent {
  id: string;
  agency: Agency;
  agencyId: string;
  createdAt: string;
  updatedAt: string;
  user: User;
}

interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  activated: boolean;
  referralCode: null;
  createdAt: string;
  applicants: any[];
  updatedAt: string;
  profile: Profile;
}

interface Profile {
  firstName: string;
  lastName: string;
  avatar: string;
  verified: boolean;
  dateOfBirth: null;
  documents: Document[];
}

interface Document {
  id: string;
  name: string;
  frontSide: null;
  backSide: null;
}
