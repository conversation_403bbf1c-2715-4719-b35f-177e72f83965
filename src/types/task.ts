// Types
export interface Assignee {
  name: string;
  avatar: string | null;
  initials: string;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  assignee: Assignee;
  dueDate: string;
  comments: number;
  attachments: number;
}

export interface Column {
  id: string;
  title: string;
  tasks: Task[];
}

export interface Columns {
  [key: string]: Column;
}

export interface Tasks {
  [key: string]: Task;
}

export interface TaskCardProps {
  task: Task;
  index: number;
}

export interface ColumnComponentProps {
  column: Column;

  onDeleteColumn: (columnId: string) => void;
}
