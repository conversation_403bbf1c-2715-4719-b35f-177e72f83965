import NextAuth from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma as db } from "./lib/prisma";
import { findUserById } from "./actions/users";
import { authConfig } from "./auth-config";

export const { auth, handlers, signOut, signIn } = NextAuth({
  adapter: PrismaAdapter(db),
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  ...authConfig,
  callbacks: {
    async session({ session, token }) {
      if (token.sub && session.user) session.user.id = token.sub;
      if (token.role && session.user) session.user.role = token.role;
      return session;
    },
    async jwt({ token }) {
      if (!token.sub) return token;
      const user = await findUserById(token.sub);
      if (!user) return token;
      token.role = user.role;
      return token;
    },
  },
});
