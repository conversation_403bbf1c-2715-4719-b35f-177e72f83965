/**
 * List of public routes that don't require authentication
 * @type {string[]}
 */
export const publicRoute = ["/", "/auth/new-verification"];

export const onboardingRoutes = [
  "/onboarding",
  "/onboarding/manager",
  "/onboarding/password",
  "/onboarding/review",
  "/onboarding/headoffice",
  "/onboarding/contact",
];
/**
 * List of authentication-related routes
 * @type {string[]}
 */
export const authRoutes = ["/auth/login", "/auth/signup", "/auth/error"];

/**
 * Prefix for authentication API endpoints
 * @type {string}
 */
export const authPrefix = "/api/";

/**
 * Default redirect path after successful login
 * @type {string}
 */
export const DEFAULT_LOGIN_REDIRECT = "/settings";
